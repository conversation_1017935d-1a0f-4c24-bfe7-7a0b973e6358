<template>
  <div class="roles-new-container" v-loading="pageLoading" element-loading-text="加载中...">
    <div class="form-container">
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="120px"
        class="role-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-form-item label="角色名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入角色名称"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="角色描述" prop="remark">
            <el-input
              type="textarea"
              v-model="form.remark"
              placeholder="请输入角色描述"
              :rows="4"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 权限管理 -->
        <div class="form-section">
          <div class="section-title">权限管理</div>
          <el-form-item label="权限列表" prop="authorities">
            <div class="authority-table-container">
              <el-table
                :data="authorityTree"
                border
                :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
                class="authority-table"
              >
                <el-table-column label="一级菜单" width="200">
                  <template slot-scope="scope">
                    <el-checkbox
                      :value="isCategoryChecked(scope.row)"
                      :indeterminate="isCategoryIndeterminate(scope.row)"
                      @change="handleCategoryCheck(scope.row, $event)"
                    >
                      {{ scope.row.title }}
                    </el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="二级菜单">
                  <template slot-scope="scope">
                    <div class="secondary-menu-horizontal">
                      <el-checkbox
                        v-for="item in scope.row.children"
                        :key="item.authority"
                        :value="checkedAuthorities.includes(item.authority)"
                        @change="handleItemCheck(item.authority, $event)"
                        class="secondary-item-horizontal"
                      >
                        {{ item.title }}
                      </el-checkbox>
                    </div>
                  </template>
                </el-table-column>

              </el-table>
            </div>
          </el-form-item>
        </div>

        <!-- 数据权限管理 -->
        <div class="form-section">
          <div class="section-title">数据权限管理</div>
          <el-form-item label="服务合同" prop="contractIds">
            <el-select
              v-model="displaySelectedContractIds"
              multiple
              filterable
              placeholder="请选择服务合同"
              style="width: 100%"
              @change="handleContractChange"
              @remove-tag="handleRemoveContract"
            >
              <el-option
                v-for="contract in allAvailableContracts"
                :key="contract.id"
                :label="contract.name"
                :value="contract.id.toString()"
              >
                {{ contract.name }}
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="onSubmit" :loading="submitting">
            {{ isEdit ? '保存' : '创建' }}
          </el-button>
          <el-button @click="$router.back()">取消</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    const validateAuthorities = (_rule, _value, callback) => {
      if (this.checkedAuthorities.length === 0) {
        callback(new Error('请至少选择一个权限'))
      } else {
        callback()
      }
    }
    return {
      form: {
        name: '',
        remark: '',
        contractIds: []
      },
      rules: {
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
        authorities: [{ validator: validateAuthorities, trigger: 'change' }]
      },
      authorityTree: [],
      contractList: [],
      checkedAuthorities: [],
      dataScopeContracts: {}, // 存储dataScopes.CONTRACT数据
      displaySelectedContractIds: [], // 用于显示的选中合同ID
      submitting: false,
      pageLoading: true
    }
  },
  computed: {
    isEdit() {
      return !!this.$route.params.id
    },
    roleId() {
      return this.$route.params.id
    },
    // 所有可用的合同列表（出现在下拉选项中）
    allAvailableContracts() {
      if (!this.isEdit) {
        // 新增模式：返回所有可选合同
        return this.contractList
      }

      // 编辑模式：返回所有合同（包括不可删除的和可选择的）
      const dataScopeContracts = this.dataScopeContracts || {}
      const allContracts = this.contractList || []
      const result = []

      // 1. 添加仅在dataScopes.CONTRACT中的合同（不可删除，显示名称）
      Object.keys(dataScopeContracts).forEach(contractId => {
        const existsInAll = allContracts.find(c => c.id.toString() === contractId)
        if (!existsInAll) {
          result.push({
            id: contractId,
            name: dataScopeContracts[contractId]
          })
        }
      })

      // 2. 添加所有allContractByCustomer中的合同（用户可以自由选择）
      allContracts.forEach(contract => {
        result.push({
          id: contract.id,
          name: contract.name
        })
      })

      return result
    },



    nonDeletableContractIds() {
      if (!this.isEdit) {
        return []
      }

      const dataScopeContracts = this.dataScopeContracts || {}
      const allContracts = this.contractList || []

      return Object.keys(dataScopeContracts).filter(contractId => {
        const existsInAll = allContracts.find(c => c.id.toString() === contractId)
        return !existsInAll
      })
    },


  },
  async created() {
    try {
      await this.loadAuthorityTree()
      await this.loadContractList()
      if (this.isEdit) {
        await this.getRoleDetails()
        this.setOperableContractIds()
      }
    } finally {
      this.pageLoading = false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.hideNonDeletableCloseButtons()
    })
  },
  updated() {
    this.$nextTick(() => {
      this.hideNonDeletableCloseButtons()
    })
  },
  methods: {
    async loadAuthorityTree() {
      const [err, r] = await client.customerGetAuthorityTree({ body: {} })
      if (err) {
        handleError(err)
        return
      }
      this.authorityTree = r.data.children || []
    },

    async loadContractList() {
      const [err, r] = await client.customerGetAllContracts({ body: {} })
      if (err) {
        handleError(err)
        return
      }
      this.contractList = r.data || []
    },

    async getRoleDetails() {
      const [err, r] = await client.customerRoleDetail({
        body: { roleId: this.roleId }
      })
      if (err) {
        handleError(err)
        return
      }
      this.form.name = r.data.name
      this.form.remark = r.data.remark
      this.checkedAuthorities = r.data.authorities || []

      this.dataScopeContracts = (r.data.dataScopes && r.data.dataScopes.CONTRACT) || {}

      this.setOperableContractIds()
    },

    setOperableContractIds() {
      const dataScopeContracts = this.dataScopeContracts || {}

      // 1. 找出既在dataScopes中又在allContractByCustomer中的合同（这些默认选中）
      const defaultSelectedIds = this.contractList
        .filter(contract => dataScopeContracts[contract.id.toString()])
        .map(contract => contract.id.toString())

      // 2. 用户可操作的合同就是默认选中的这些
      this.form.contractIds = defaultSelectedIds

      // 3. 设置显示的选中合同ID（包括不可删除的）
      const nonDeletableIds = this.nonDeletableContractIds
      this.displaySelectedContractIds = [...nonDeletableIds, ...defaultSelectedIds]
    },

    handleContractChange(value) {
      if (!this.isEdit) {
        this.form.contractIds = value
        this.displaySelectedContractIds = value
        return
      }

      const nonDeletableIds = this.nonDeletableContractIds

      // 确保不可删除的合同始终存在
      const mustHaveIds = [...nonDeletableIds]
      const userSelectedIds = value.filter(id => !nonDeletableIds.includes(id))

      this.displaySelectedContractIds = [...mustHaveIds, ...userSelectedIds]

      this.form.contractIds = userSelectedIds
    },

    isCategoryChecked(category) {
      if (!category.children || category.children.length === 0) return false
      return category.children.every(item => this.checkedAuthorities.includes(item.authority))
    },

    isCategoryIndeterminate(category) {
      if (!category.children || category.children.length === 0) return false
      const checkedCount = category.children.filter(item => this.checkedAuthorities.includes(item.authority)).length
      return checkedCount > 0 && checkedCount < category.children.length
    },

    getOperationFunctions(category) {
      // 获取操作功能权限，这里可以根据实际需求过滤
      if (!category.children) return []
      return category.children.filter(item => {
        // 可以根据权限名称或其他标识来判断是否为操作功能
        return item.title.includes('新增') || item.title.includes('编辑') ||
               item.title.includes('删除') || item.title.includes('查看') ||
               item.title.includes('导出') || item.title.includes('导入')
      })
    },

    handleCategoryCheck(category, checked) {
      if (!category.children) return

      category.children.forEach(item => {
        const index = this.checkedAuthorities.indexOf(item.authority)
        if (checked && index === -1) {
          this.checkedAuthorities.push(item.authority)
        } else if (!checked && index > -1) {
          this.checkedAuthorities.splice(index, 1)
        }
      })
      this.handleCheckChange()
    },

    handleItemCheck(authority, checked) {
      const index = this.checkedAuthorities.indexOf(authority)
      if (checked && index === -1) {
        this.checkedAuthorities.push(authority)
      } else if (!checked && index > -1) {
        this.checkedAuthorities.splice(index, 1)
      }
      this.handleCheckChange()
    },

    handleRemoveContract(contractId) {
      const contractIdStr = contractId.toString()

      // 如果是不可删除的合同，阻止删除并恢复
      if (this.nonDeletableContractIds.includes(contractIdStr)) {
        this.$nextTick(() => {
          const nonDeletableIds = this.nonDeletableContractIds
          const userSelectedIds = this.form.contractIds
          this.displaySelectedContractIds = [...nonDeletableIds, ...userSelectedIds]
        })
        this.$message.warning('该合同已分配，不可删除')
      }
    },

    hideNonDeletableCloseButtons() {
      if (!this.isEdit || this.nonDeletableContractIds.length === 0) {
        return
      }

      this.$nextTick(() => {
        const selectElement = this.$el.querySelector('.el-select')
        if (!selectElement) return

        const tags = selectElement.querySelectorAll('.el-tag')
        tags.forEach(tag => {
          const tagText = tag.querySelector('.el-select__tags-text')
          if (tagText) {
            const contractName = tagText.textContent.trim()
            // 查找对应的合同ID（从dataScopeContracts中查找）
            const dataScopeContracts = this.dataScopeContracts || {}
            const contractId = Object.keys(dataScopeContracts).find(id =>
              dataScopeContracts[id] === contractName
            )

            if (contractId && this.nonDeletableContractIds.includes(contractId)) {
              const closeButton = tag.querySelector('.el-tag__close')
              if (closeButton) {
                closeButton.style.display = 'none'
              }
            }
          }
        })
      })
    },

    handleCheckChange() {
      this.$refs.form.validateField('authorities')
    },

    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true
      const roleId = this.$route.params.id

      // 合并不可删除的合同和用户选择的合同
      const allContractIds = [
        ...this.nonDeletableContractIds, // 不可删除的合同
        ...this.form.contractIds // 用户选择的可操作合同
      ]

      const payload = {
        name: this.form.name,
        remark: this.form.remark,
        authorities: this.checkedAuthorities,
        contractIds: [...new Set(allContractIds)] // 去重
      }

      try {
        if (this.isEdit) {
          payload.roleId = roleId
          const [err] = await client.customerEditRole({ body: payload })
          if (err) {
            handleError(err)
            return
          }
        } else {
          const [err] = await client.customerAddRole({ body: payload })
          if (err) {
            handleError(err)
            return
          }
        }

        this.$message.success(this.isEdit ? '更新成功' : '创建成功')
        this.$router.push('/roles')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.roles-new-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
}

.role-form {
  width: 100%;
}

.form-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input,
.el-textarea {
  width: 100%;
}

.authority-table-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.authority-table {
  width: 100%;
}

.authority-table .el-table__body-wrapper {
  max-height: 500px;
  overflow-y: auto;
}

.secondary-menu-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.secondary-item-horizontal {
  margin-right: 0 !important;
  margin-bottom: 0;
  white-space: nowrap;
}

/* 角色新增/编辑页面样式 */

.no-data {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 20px 0;
}

.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 下拉框中的复选框样式 */
.contract-select-dropdown .el-select-dropdown__item {
  padding: 0;
  height: auto;
}

.contract-select-dropdown .el-checkbox {
  width: 100%;
  padding: 8px 20px;
  margin: 0;
}

.contract-select-dropdown .el-checkbox:hover {
  background-color: #f5f7fa;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .roles-new-container {
    padding: 20px 12px;
  }

  .form-container {
    padding: 24px;
  }

  .authority-table {
    font-size: 12px;
  }

  .operation-functions {
    flex-direction: column;
  }

  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
