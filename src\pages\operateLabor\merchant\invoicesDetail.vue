<template>
  <div class="invoice-detail-container" v-loading="loading" element-loading-text="加载中...">
    <div class="detail-container">
      <!-- 基本信息 -->
      <div class="summary-container">
        <div class="summary-title">基本信息</div>
        <div class="summary-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <label>客户：</label>
                <span>{{ summaryData.customerName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>开票主体：</label>
                <span>{{ summaryData.supplierCorporationName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>发票类型：</label>
                <span>{{ summaryData.typeDesc || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>申请编号：</label>
                <span>{{ summaryData.sn || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>状态：</label>
                <span>{{ summaryData.statusDesc || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>开票金额：</label>
                <span>{{ formatAmount(summaryData.fee) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>申请时间：</label>
                <span>{{ summaryData.createTime || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 发票信息 -->
      <div class="summary-container">
        <div class="summary-title">发票信息</div>
        <div class="summary-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <label>发票抬头：</label>
                <span>{{ summaryData.title || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>纳税人识别号：</label>
                <span>{{ summaryData.taxNo || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>开户行：</label>
                <span>{{ summaryData.bankName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>银行账号：</label>
                <span>{{ summaryData.bankAccount || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>注册地址：</label>
                <span>{{ summaryData.registerAddress || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>企业电话：</label>
                <span>{{ summaryData.companyTel || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="summary-item remark-item">
                <label>发票备注：</label>
                <span class="remark-content">{{ summaryData.remark || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 发票明细列表 -->
      <div class="table-container">
        <div class="summary-title">发票明细</div>
        <el-table
          :data="tableData"
          v-loading="false"
          :stripe="false"
          highlight-current-row
          :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
          row-key="billNo"
          show-summary
          :summary-method="getSummaries"
        >
          <template slot="empty">
            <div class="empty-data">暂无数据</div>
          </template>
          <el-table-column
            prop="billNo"
            label="账单编号"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.billNo || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="billMonth"
            label="账单月份"
            width="150"
          >
            <template slot-scope="scope">
              {{ scope.row.billMonth || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="invoiceCategory"
            label="发票类目"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.invoiceCategory || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="fee"
            label="开票金额"
            width="150"
          >
            <template slot-scope="scope">
              {{ formatAmount(scope.row.fee) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import formatAmount from 'kit/formatters/formatAmount'

const client = makeClient()

export default {
  name: 'InvoiceDetail',
  data() {
    return {
      loading: true,
      summaryData: {},
      tableData: []
    }
  },
  async created() {
    await this.getInvoiceDetail()
  },
  methods: {
    formatAmount,
    async getInvoiceDetail() {
      this.loading = true
      try {
        const invoiceId = this.$route.params.id
        if (!invoiceId) {
          this.$message.error('未找到发票ID')
          return
        }
        const [err, res] = await client.costomerInvoicesDetail({
          body: {
            invoiceId: parseInt(invoiceId, 10)
          }
        })
        if (err) {
          handleError(err)
          return
        }
        this.summaryData = res.data
        this.tableData = res.data.items || []
      } finally {
        this.loading = false
      }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((_, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        // 检查是否是开票金额列（最后一列）
        if (index === columns.length - 1) {
          const values = data.map(item => Number(item.fee))
          if (!values.every(value => isNaN(value))) {
            const total = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = formatAmount(total)
          } else {
            sums[index] = 'N/A'
          }
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>

<style scoped>
.invoice-detail-container {
  overflow-y: auto;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  padding: 12px;
  box-sizing: border-box;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-container {
  background: #fff;
  border-radius: 8px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.summary-content {
  margin-top: 20px;
}

.summary-item {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 32px;
  display: flex;
  align-items: flex-start;
}

.summary-item label {
  display: inline-block;
  width: 120px;
  text-align: right;
  margin-right: 8px;
  color: #909399;
  flex-shrink: 0;
}

.summary-item span {
  color: #606266;
  word-break: break-all;
}

.remark-item {
  align-items: flex-start;
}


.table-container {
  background: #fff;
  border-radius: 8px;
  min-height: 400px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.el-table {
  margin-top: 12px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 12px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 14px;
}

.el-table td,
.el-table--medium td {
  padding: 12px 0;
  color: #606266;
  font-size: 14px;
}

.el-table .el-table__body tr:hover > td {
  background-color: #f5f7fa;
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .detail-container {
    max-width: 100%;
    margin: 0;
  }
}

@media screen and (max-width: 768px) {
  .invoice-detail-container {
    padding: 8px;
  }

  .summary-container {
    padding: 16px;
  }

  .table-container {
    padding: 12px;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .summary-item label {
    width: auto;
    text-align: left;
    margin-bottom: 4px;
  }

  .el-col {
    margin-bottom: 8px;
  }
}
</style>
