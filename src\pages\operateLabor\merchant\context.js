import store from 'kit/helpers/store'

const setCustomerUserProfile = profile => {
  store.set('customerUserProfile', profile)
}

const getCustomerUserProfile = () => {
  const customerUserProfile = store.get('customerUserProfile')
  return JSON.parse(customerUserProfile)
}

// 清理所有与域名相关的本地存储数据
const clearDomainRelatedData = () => {
  localStorage.removeItem('domainInfo')
  localStorage.removeItem('customerUserProfile')
  localStorage.removeItem('smsCountdownEndTime')
  localStorage.removeItem('__authorities__')
  localStorage.removeItem('currentCustomer')
  localStorage.removeItem('availableCustomers')
}

// 检查当前域名是否与存储的域名信息匹配
const isDomainInfoValid = () => {
  const domainInfo = localStorage.getItem('domainInfo')
  if (!domainInfo) {
    return false
  }

  try {
    const parsedDomainInfo = JSON.parse(domainInfo)
    let currentDomain = window.location.host

    if (currentDomain.includes('localhost')) {
      currentDomain = '156-dev.olading.com'
    }

    return parsedDomainInfo.domainName === currentDomain
  } catch (error) {
    console.error('Failed to parse domainInfo:', error)
    return false
  }
}

export {
  setCustomerUserProfile,
  getCustomerUserProfile,
  clearDomainRelatedData,
  isDomainInfoValid
}
