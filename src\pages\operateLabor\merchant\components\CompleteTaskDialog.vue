<template>
  <el-dialog
    title="完成任务"
    :visible.sync="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    class="complete-task-dialog"
    @close="handleClose"
  >
    <div class="complete-task-content">
      <!-- 提示信息 -->
      <div class="tip-banner">
        <i class="el-icon-warning" style="color: #E6A23C; margin-right: 8px;"></i>
        最多上传5个文件
      </div>

      <!-- 表单内容 -->
      <el-form label-width="80px" class="complete-form">
        <el-form-item label="任务名称">
          <span class="task-name-display">{{ task?.taskName || '-' }}</span>
        </el-form-item>

        <el-form-item label="完成凭证">
          <div class="upload-container">
            <!-- 文件预览网格 -->
            <div class="file-grid">
              <!-- 已上传的文件 -->
              <div
                v-for="(file, index) in fileList"
                :key="index"
                class="file-preview-item"
              >
                <!-- 图片预览 -->
                <div v-if="isImageFile(file)" class="image-preview">
                  <img :src="getFilePreviewUrl(file)" :alt="file.name" class="preview-image" />
                  <div class="file-overlay">
                    <span class="file-name">{{ file.name }}</span>
                    <div class="file-actions">
                      <i class="el-icon-zoom-in" @click="previewImage(file)" title="预览"></i>
                      <i class="el-icon-delete" @click="removeFile(index)" title="删除"></i>
                    </div>
                  </div>
                  <div v-if="file.status === 'success'" class="upload-status success">
                    <i class="el-icon-circle-check"></i>
                  </div>
                </div>

                <!-- 非图片文件预览 -->
                <div v-else class="file-preview">
                  <div class="file-icon-container">
                    <i
                      :class="getFileIcon(file).icon"
                      class="file-type-icon"
                      :style="{ color: getFileIcon(file).color }"
                    ></i>
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="file.name">{{ file.name }}</span>
                  </div>
                  <div class="file-overlay">
                    <span class="file-name">{{ file.name }}</span>
                    <div class="file-actions">
                      <i class="el-icon-delete" @click="removeFile(index)" title="删除"></i>
                    </div>
                  </div>
                  <div v-if="file.status === 'success'" class="upload-status success">
                    <i class="el-icon-circle-check"></i>
                  </div>
                </div>
              </div>

              <!-- 上传按钮 -->
              <div v-if="fileList.length < 5" class="upload-item">
                <el-upload
                  ref="upload"
                  class="complete-task-upload"
                  :headers="headerToken"
                  :file-list="fileList"
                  :auto-upload="false"
                  :on-change="onFileChange"
                  :on-remove="onFileRemove"
                  :on-success="onFileSuccess"
                  :on-error="onFileError"
                  accept=".pdf,.png,.jpg,.jpeg,.doc,.docx,.xls,.xlsx"
                  :action="uploadUrl"
                  :show-file-list="false"
                  multiple
                >
                  <div class="upload-box">
                    <i class="el-icon-plus upload-icon"></i>
                    <span class="upload-text">上传文件</span>
                  </div>
                </el-upload>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        完成
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import handleSuccess from '../../../../helpers/handleSuccess'
import makeClient from '../../../../services/operateLabor/makeClient'
import { getToken } from '../../../../helpers/token'

const client = makeClient()

export default {
  name: 'CompleteTaskDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    task: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      fileList: [],
      uploadedFileIds: [],
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    uploadUrl() {
      return `${window.env?.apiPath}/api/public/uploadFile`
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    resetForm() {
      this.fileList = []
      this.uploadedFileIds = []
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
    },

    handleClose() {
      this.dialogVisible = false
    },

    handleCancel() {
      this.dialogVisible = false
    },

    async handleConfirm() {
      if (this.fileList.length === 0) {
        this.$message.error('请上传完成凭证')
        return
      }

      // 检查文件数量限制
      if (this.fileList.length > 5) {
        this.$message.error('最多只能上传5个文件')
        return
      }

      this.loading = true

      try {
        // 上传所有文件
        for (const file of this.fileList) {
          if (file.status !== 'success') {
            await this.uploadSingleFile(file)
          }
        }

        // 调用完成任务接口
        const [err] = await client.customerFinishTask({
          body: {
            taskId: this.task.taskId,
            finishCredentials: this.uploadedFileIds
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('任务完成成功')
        this.dialogVisible = false
        this.$emit('complete', this.task)
      } catch (error) {
        console.error('完成任务失败：', error)
        this.$message.error('完成任务失败，请重试')
      } finally {
        this.loading = false
      }
    },

    async uploadSingleFile(file) {
      return new Promise((resolve, reject) => {
        const formData = new FormData()
        formData.append('file', file.raw)

        const xhr = new XMLHttpRequest()
        xhr.open('POST', this.uploadUrl)
        xhr.setRequestHeader('Authorization', this.headerToken.Authorization)

        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              if (response.success && response.data) {
                this.uploadedFileIds.push(response.data.fileId.toString())
                file.status = 'success'
                resolve(response)
              } else {
                reject(new Error(response.message || '上传失败'))
              }
            } catch (e) {
              reject(new Error('解析响应失败'))
            }
          } else {
            reject(new Error(`上传失败: ${xhr.status}`))
          }
        }

        xhr.onerror = () => {
          reject(new Error('网络错误'))
        }

        xhr.send(formData)
      })
    },

    onFileChange(file, fileList) {
      // 限制文件数量
      if (fileList.length > 5) {
        this.$message.warning('最多只能上传5个文件')
        fileList.splice(5) // 移除超出的文件
      }

      // 为新添加的文件设置预览URL
      if (file && this.isImageFile(file)) {
        file.previewUrl = this.getFilePreviewUrl(file)
      }

      this.fileList = fileList
    },

    onFileRemove(file, fileList) {
      this.fileList = fileList
      // 从已上传文件ID列表中移除对应的文件ID
      if (file.response?.data) {
        const fileId = file.response.data.fileId.toString()
        const index = this.uploadedFileIds.indexOf(fileId)
        if (index > -1) {
          this.uploadedFileIds.splice(index, 1)
        }
      }
    },

    removeFile(index) {
      // 获取要删除的文件
      const fileToRemove = this.fileList[index]

      // 从已上传文件ID列表中移除
      if (fileToRemove.response?.data) {
        const fileId = fileToRemove.response.data.fileId.toString()
        const fileIdIndex = this.uploadedFileIds.indexOf(fileId)
        if (fileIdIndex > -1) {
          this.uploadedFileIds.splice(fileIdIndex, 1)
        }
      }

      // 从文件列表中移除
      this.fileList.splice(index, 1)
    },

    onFileSuccess(response, file, fileList) {
      console.log('文件上传成功：', response)
      if (response.success && response.data) {
        this.uploadedFileIds.push(response.data.fileId.toString())
        file.status = 'success'
      }
    },

    onFileError(err, file, fileList) {
      console.error('文件上传错误：', err)
      this.$message.error(`文件 ${file.name} 上传失败`)
    },

    // 文件处理相关方法
    isImageFile(file) {
      const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      return imageTypes.includes(file.raw?.type) || /\.(jpg|jpeg|png|gif|webp)$/i.test(file.name)
    },

    getFileIcon(file) {
      const fileName = file.name.toLowerCase()

      // PDF文件
      if (fileName.endsWith('.pdf')) {
        return { icon: 'el-icon-document-copy', color: '#ff4757' }
      }
      // Word文档
      else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
        return { icon: 'el-icon-document', color: '#2f5597' }
      }
      // Excel文件
      else if (fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) {
        return { icon: 'el-icon-s-grid', color: '#1e7e34' }
      }
      // 图片文件
      else if (this.isImageFile(file)) {
        return { icon: 'el-icon-picture-outline', color: '#6f42c1' }
      }
      // 其他文件
      else {
        return { icon: 'el-icon-tickets', color: '#409eff' }
      }
    },

    getFilePreviewUrl(file) {
      if (file.raw) {
        return URL.createObjectURL(file.raw)
      }
      return ''
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },

    previewImage(file) {
      // 创建图片预览对话框
      const imageUrl = this.getFilePreviewUrl(file)
      this.$alert(`<img src="${imageUrl}" style="width: 600px; height: 500px;" />`, file.name, {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false,
        showClose: true,
        customClass: 'image-preview-dialog'
      })
    }
  }
}
</script>

<style scoped>
.complete-task-dialog {
  .tip-banner {
    background: #fdf6ec;
    border: 1px solid #f5dab1;
    border-radius: 4px;
    padding: 7px 16px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #e6a23c;
  }

  .complete-form {
    .task-name-display {
      font-weight: 500;
      color: #303133;
    }
  }

  .upload-container {
    .file-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 12px;
      min-height: 120px;
    }

    .file-preview-item {
      position: relative;
      width: 120px;
      height: 120px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      overflow: hidden;
      background: #fff;

      &:hover .file-overlay {
        opacity: 1;
      }
    }

    .image-preview {
      position: relative;
      width: 100%;
      height: 100%;

      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file-preview {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .file-icon-container {
        .file-type-icon {
          font-size: 20px;
        }
      }

      .file-info {
        text-align: center;
        width: 100%;

        .file-name {
          display: block;
          font-size: 12px;
          color: #606266;
          margin-bottom: 4px;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }

    .file-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 8px;
      opacity: 0;
      transition: opacity 0.3s;

      .file-name {
        font-size: 12px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .file-actions {
        display: flex;
        justify-content: center;
        gap: 12px;

        i {
          cursor: pointer;
          font-size: 16px;
          padding: 4px;
          border-radius: 2px;
          transition: background-color 0.3s;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }

    .upload-status {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.success {
        background: #67c23a;
        color: white;

        i {
          font-size: 12px;
        }
      }
    }

    .upload-item {
      width: 120px;
      height: 120px;
    }

    .complete-task-upload {
      width: 100%;
      height: 100%;

      .upload-box {
        width: 120px;
        height: 120px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }

        .upload-icon {
          font-size: 28px;
          color: #c0c4cc;
          margin-bottom: 8px;
        }

        .upload-text {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

/* 图片预览对话框样式 */
:global(.image-preview-dialog) {
  .el-message-box__content {
    text-align: center;
  }

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}
</style>
