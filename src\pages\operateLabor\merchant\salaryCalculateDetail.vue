<template>
  <div class="salary-detail-container" v-loading="loading" element-loading-text="加载中...">
    <div class="detail-container">
      <!-- 摘要信息 -->
      <div class="summary-container">
        <!-- <div class="summary-title">工资表详情</div> -->
        <el-row :gutter="40">
          <el-col :span="8">
            <div class="summary-item">
              <label>客户：</label>
              <span>{{ summaryData.customerName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>服务合同：</label>
              <span>{{ summaryData.contractName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>作业主体：</label>
              <span>{{ summaryData.supplierCorporationName || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="8">
            <div class="summary-item">
              <label>税款所属期：</label>
              <span>{{ summaryData.taxPeriod || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>总人数：</label>
              <span>{{ summaryData.totalPeople || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>总应发金额：</label>
              <span>{{ summaryData.totalPayable || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="8">
            <div class="summary-item">
              <label>总实发金额：</label>
              <span>{{ summaryData.netPaymentTotal || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>状态：</label>
              <span :class="['status-tag', getStatusClass(summaryData.status)]">
                {{ getStatusText(summaryData.status) }}
              </span>
            </div>
          </el-col>
        </el-row>
        <!-- 发薪超10w人员警告 -->
      <div v-if="overStaffList.length > 0" class="over-staff-warning" style="display: flow">
        <i class="el-icon-warning"></i>
        <span class="warning-text">
          提示！检测到 <strong class="warning-count">{{ overStaffList.length }}</strong> 名人员平台月累计收入达到增值税起征点，存在补缴增值税风险：
        </span>
        <el-button type="text" class="view-detail-btn" @click="showOverStaffDialog = true">
          查看详情
        </el-button>
      </div>
      </div>

      

      <!-- 工资明细列表 -->
      <div class="table-container">
        <!-- <div class="table-header">
          <div class="table-title">工资明细</div>
          <div class="table-actions">
            <el-button type="primary" size="small" @click="handleDownload" :loading="downloading">
              <i class="el-icon-download"></i>
              下载明细
            </el-button>
          </div>
        </div> -->

        <el-table
          :data="tableData"
          :height="tableHeight"
          :stripe="false"
          highlight-current-row
          :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
          v-loading="tableLoading"
        >
          <template slot="empty">
            <div class="empty-data">暂无数据</div>
          </template>
          <el-table-column prop="name" label="姓名" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="idCard" label="身份证" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.idCard || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="phoneNumber" label="手机号" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.phoneNumber || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="payableAmount" label="应发金额" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.payableAmount || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="netPayment" label="实发金额" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.netPayment || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="accumulatedIncome" label="累计收入" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.accumulatedIncome || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="accumulatedTaxAmount" label="累计应纳税额" min-width="150">
            <template slot-scope="scope">
              {{ scope.row.accumulatedTaxAmount || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="accumulatedTaxableAmount" label="累计应纳税所得额" min-width="150">
            <template slot-scope="scope">
              {{ scope.row.accumulatedTaxableAmount || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="accumulatedPrepaidTax" label="累计已预缴税额" min-width="150">
            <template slot-scope="scope">
              {{ scope.row.accumulatedPrepaidTax || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="currentTaxAmount" label="本期应预扣预缴税额" min-width="180">
            <template slot-scope="scope">
              {{ scope.row.currentTaxAmount || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="currentWithholdingTax" label="本次应预扣预缴税额" min-width="180">
            <template slot-scope="scope">
              {{ scope.row.currentWithholdingTax || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="vatAmount" label="增值税额" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.vatAmount || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="urbanConstructionTax" label="城市维护建设税" min-width="150">
            <template slot-scope="scope">
              {{ scope.row.urbanConstructionTax || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="educationSurcharge" label="教育费附加" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.educationSurcharge || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="localEducationSurcharge" label="地方教育附加" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.localEducationSurcharge || '-' }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="conditions.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 发薪超10w人员详情对话框 -->
    <el-dialog
      :title="`人员详情（共${overStaffList.length}人）`"
      :visible.sync="showOverStaffDialog"
      width="650px"
      :close-on-click-modal="false"
      class="over-staff-dialog"
    >
      <div class="over-staff-content">
        <el-table :data="overStaffList" style="width: 100%" stripe>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" width="120" align="center">
            <template slot-scope="scope">
              <span class="staff-name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="idCard" label="身份证号" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="staff-id-card">{{ scope.row.idCard }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showOverStaffDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()


export default {
  data() {
    return {
      loading: true,
      downloading: false,
      tableLoading: false,
      summaryData: {},
      tableData: [],
      total: 0,
      tableHeight: 500,
      overStaffList: [],
      showOverStaffDialog: false,
      conditions: {
        offset: 0,
        limit: 10,
        filters: {
            id: parseInt(this.$route.params.id)
        },
        withTotal: true
      },
      statusOptions: {
        'CALCULATING': '算税中',
        'UNCONFIRMED': '待确认',
        'CONFIRMED': '已确认',
        'DRAFT': '草稿',
        'SUBMITTED': '已提交',
        'APPROVED': '已审核',
        'PAID': '已发放'
      }
    }
  },
  async created() {
    this.loading = true
    await Promise.all([
      this.loadDetail(),
      this.loadOverStaffCount(),
      this.loadSummaryData()
    ])
    this.loading = false
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadDetail() {
      this.tableLoading = true
      const id = this.$route.params.id

      const [err, r] = await client.customerSalaryListPayrollDetail({
        body: {
          ...this.conditions
        }
      })

      this.tableLoading = false

      if (err) {
        console.error('加载详情失败：', err)
        this.$message.error('加载详情失败，请重试')
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0

      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    async loadSummaryData() {
      const id = this.$route.params.id

      const [err, r] = await client.customerSalaryListPayroll({
        body: {
          offset: 0,
          limit: 1,
          withTotal: false,
          filters: {
            id: parseInt(id)
          }
        }
      })

      if (err) {
        console.error('加载摘要数据失败：', err)
        return
      }

      if (r.data && r.data.list && r.data.list.length > 0) {
        this.summaryData = r.data.list[0] || {}
      }
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.loadDetail()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.loadDetail()
    },

    getStatusText(status) {
      return this.statusOptions[status] || status || '-'
    },

    getStatusClass(status) {
      const classMap = {
        'CALCULATING': 'status-calculating',
        'UNCONFIRMED': 'status-unconfirmed',
        'CONFIRMED': 'status-confirmed',
        'DRAFT': 'status-draft',
        'SUBMITTED': 'status-submitted',
        'APPROVED': 'status-approved',
        'PAID': 'status-paid'
      }
      return classMap[status] || 'status-default'
    },

    getStatusTagType(status) {
      const typeMap = {
        'CALCULATING': 'warning',
        'UNCONFIRMED': 'warning',
        'CONFIRMED': 'success',
        'DRAFT': 'info',
        'SUBMITTED': 'warning',
        'APPROVED': 'success',
        'PAID': 'success'
      }
      return typeMap[status] || 'info'
    },

    async handleDownload() {
      this.downloading = true
      const id = this.$route.params.id

      const [err, res] = await client.customerSalaryPayRollDetailDownload({
        body: { id }
      })

      this.downloading = false

      if (err) {
        console.error('下载工资表明细失败：', err)
        this.$message.error('下载工资表明细失败')
        return
      }

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([res]))
      const link = document.createElement('a')
      link.href = url
      link.download = `工资表明细_${id}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      this.$message.success('工资表明细下载成功')
    },

    async loadOverStaffCount() {
      const id = this.$route.params.id

      const [err, r] = await client.customerSalarySalaryOverStaffCount({
        body: { id: parseInt(id) }
      })

      if (!err && r && r.success && r.data && Array.isArray(r.data)) {
        // 解析返回的数据格式：["姓名-身份证号"]
        this.overStaffList = r.data.map(item => {
          const dashIndex = item.lastIndexOf('-')
          if (dashIndex > 0 && dashIndex < item.length - 1) {
            return {
              name: item.substring(0, dashIndex).trim(),
              idCard: item.substring(dashIndex + 1).trim()
            }
          }
          return null
        }).filter(item => item !== null)
      }
    },

    setTableHeight() {
      const windowHeight = window.innerHeight;
      const summaryContainer = this.$el.querySelector('.summary-container');
      const summaryHeight = summaryContainer ? summaryContainer.offsetHeight : 0;
      const tableHeader = this.$el.querySelector('.table-header');
      const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
      const pagination = this.$el.querySelector('.pagination-container');
      const paginationHeight = pagination ? pagination.offsetHeight : 40;
      const padding = 80;

      const availableHeight = windowHeight - summaryHeight - tableHeaderHeight - paginationHeight - padding;

      if (this.tableData.length <= 5) {
        this.tableHeight = 500;
      } else {
        const minHeight = 300;
        const maxHeight = windowHeight - summaryHeight - tableHeaderHeight - paginationHeight - padding - 5;
        this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
      }

      this.tableHeight = Math.floor(this.tableHeight);
    }
  }
}
</script>

<style scoped>
.salary-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.summary-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 0 0 auto;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.summary-item {
  line-height: 32px;
  font-size: 14px;
  color: #606266;
}

.summary-item label {
  display: inline-block;
  width: 120px;
  text-align: right;
  color: #909399;
  margin-right: 8px;
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions .el-button {
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 13px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.table-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.table-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

/* 发薪超10w人员警告样式 */
.over-staff-warning {
  margin-top: 10px;
  padding: 8px 10px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  color: #d48806;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 状态标签样式 */
.status-tag {
  padding: 7px 7px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-calculating {
  background-color: #fff7e6;
  color: #d48806;
  border: 1px solid #ffd591;
}

.status-unconfirmed {
  background-color: #fff2e8;
  color: #d4380d;
  border: 1px solid #ffbb96;
}

.status-confirmed {
  background-color: #f6ffed;
  color: #389e0d;
  border: 1px solid #b7eb8f;
}

.status-draft {
  background-color: #f0f0f0;
  color: #666666;
  border: 1px solid #d9d9d9;
}

.status-submitted {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-approved {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-paid {
  background-color: #f6ffed;
  color: #389e0d;
  border: 1px solid #b7eb8f;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}


.over-staff-warning .el-icon-warning {
  font-size: 16px;
  color: #fa8c16;
}

.warning-text {
  font-weight: 400;
  font-size: 13px;
  flex: 1;
}

.warning-count {
  color: #d4380d;
  font-weight: 600;
}

.view-detail-btn {
  color: #1890ff;
  padding: 0;
  font-weight: 400;
  font-size: 13px;
  transition: all 0.3s;
}

.view-detail-btn:hover {
  color: #40a9ff;
}

/* 对话框样式 */
.over-staff-dialog {
  border-radius: 8px;
}

.over-staff-content {
  max-height: 400px;
  overflow-y: auto;
}

.staff-name {
  font-weight: 500;
  color: #303133;
}

.staff-id-card {
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  color: #606266;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .salary-detail-container {
    padding: 12px;
  }
  
  .summary-container {
    padding: 16px;
  }
  
  .table-container {
    padding: 12px;
  }
}
</style>
