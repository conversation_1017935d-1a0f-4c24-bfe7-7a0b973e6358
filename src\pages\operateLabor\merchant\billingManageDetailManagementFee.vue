<template>
  <div class="management-fee-detail-container">
    <!-- 基本信息 -->
    <div class="info-container">
      <div class="info-content">
        <div class="info-item">
          <span class="label">结算项目：</span>
          <span class="value">{{ info.feeTypeDesc }}</span>
        </div>
        <div class="info-item">
          <span class="label">结算月份：</span>
          <span class="value">{{ info.billMonth }}</span>
        </div>
        <div class="info-item">
          <span class="label">服务合同：</span>
          <span class="value">{{ info.contractName }}</span>
        </div>
        <div class="info-item">
          <span class="label">办理人数：</span>
          <span class="value">{{ info.personCount }}</span>
        </div>
        <div class="info-item">
          <span class="label">应收金额：</span>
          <span class="value">{{ info.totalAmount || '0.00' }}</span>
        </div>
      </div>
    </div>

    

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
            <el-form-item>
              <el-input
                v-model="conditions.filters.name"
                placeholder="请输入员工姓名查询"
                clearable
                @keyup.enter.native="getList"
              />
            </el-form-item>
             <el-button type="primary" @click="getList">查询</el-button>
              <el-button style="margin-left: 0px;" @click="onReset">重置</el-button>
          </div>
          <div class="search-actions">
            <el-button :loading="exportLoading" @click="exportList">导出</el-button>
          </div>
        </div>
      </el-form>
    </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        row-key="id"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        
        <el-table-column prop="laborName" label="姓名" min-width="120" show-overflow-tooltip />
        <el-table-column prop="idCard" label="身份证号" min-width="160" />
        <el-table-column prop="calculationRule" label="收费规则" min-width="120" />
        <el-table-column label="费率(%)/金额(元)" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.calculationBase ? scope.row.calculationBase + '元' : scope.row.calculationRate + '%' }}
          </template>
        </el-table-column>
        <el-table-column prop="managementFeeAmount" label="收费金额" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.managementFeeAmount || '0.00' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import { getToken } from '../../../helpers/token'

const client = makeClient()

export default {
  data() {
    return {
      info: {},
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          billMasterId: 0,
          name: ''
        }
      },
      tableData: [],
      total: 0,
      loading: false,
      exportLoading: false,
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      tableHeight: 500
    }
  },
  
  async created() {
    // 从路由参数获取信息
    this.info = {
      ...JSON.parse(this.$route.query.rowData || '{}'),
      contractName: this.$route.query.contractName
    }
    this.conditions.filters.billMasterId = this.$route.query.billMasterId
    
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  
  methods: {
    async getList() {
      this.loading = true

      const [err, r] = await client.apiCustomerBillsManagementFeeDetails({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0
      
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions.filters.name = ''
      this.conditions.offset = 0
      this.getList()
    },

    async exportList() {
      // 防重点击
      if (this.exportLoading) {
        return
      }

      this.exportLoading = true

      try {
        // 使用fetch API携带token下载文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/customer/bills/exportManagementFee`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.conditions)
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        let fileName = '管理费用列表.zip'

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败：', error)
        this.$message.error('导出失败，请重试')
      } finally {
        this.exportLoading = false
      }
    },

    setTableHeight() {
      const windowHeight = window.innerHeight
      const infoContainer = this.$el.querySelector('.info-container')
      const searchContainer = this.$el.querySelector('.search-container')
      const pagination = this.$el.querySelector('.pagination-container')

      const infoHeight = infoContainer ? infoContainer.offsetHeight : 0
      const searchHeight = searchContainer ? searchContainer.offsetHeight : 0
      const paginationHeight = pagination ? pagination.offsetHeight : 40
      const padding = 60

      const availableHeight = windowHeight - infoHeight - searchHeight - paginationHeight - padding
      const minHeight = 300
      const maxHeight = windowHeight - infoHeight - searchHeight - paginationHeight - padding - 20

      this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight))
      this.tableHeight = Math.floor(this.tableHeight)
    }
  }
}
</script>

<style scoped>
.management-fee-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.info-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.info-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.info-content {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 32px;
}

.info-item {
  display: flex;
  align-items: center;
  min-width: 200px;
  font-size: 15px;
}

.label {
  font-weight: 500;
  color: #606266;
}

.value {
  color: #303133;
  font-weight: 500;
}

.search-container {
  background: #fff;
  padding: 5px 0px 5px 0px;
  margin-bottom: 12px;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 120px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__content {
  width: 200px;
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

@media screen and (max-width: 768px) {
  .management-fee-detail-container {
    padding: 12px;
  }
  
  .info-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .info-item {
    min-width: auto;
  }
  
  .search-header {
    padding-right: 0;
    flex-direction: column;
    gap: 16px;
  }
  
  .search-actions {
    position: static;
    justify-content: flex-start;
  }
}
</style>
