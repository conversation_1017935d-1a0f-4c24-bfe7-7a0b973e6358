<template>
  <div class="merchant-users-new-container" v-loading="pageLoading" element-loading-text="加载中...">
    <!-- 主要内容区域 -->
      <div class="form-container">
        <el-form
          :model="form"
          :rules="rules"
          ref="form"
          label-width="120px"
          class="user-form"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <el-form-item label="姓名" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入姓名"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="手机号" prop="cellphone">
              <el-input
                v-model="form.cellphone"
                placeholder="请输入手机号"
                maxlength="11"
                @input="handlePhoneInput"
              />
            </el-form-item>
          </div>

          <!-- 权限配置 -->
          <div class="form-section">
            <div class="section-title">权限配置</div>
            <el-form-item label="角色名称" prop="roleIds">
              <el-select
                v-model="form.roleIds"
                multiple
                placeholder="请选择角色"
                style="width: 100%"
              >
                <el-option
                  v-for="role in roleOptions"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button type="primary" @click="onSubmit" :loading="submitting">
              {{ isEdit ? '保存' : '创建' }}
            </el-button>
            <el-button @click="$router.back()">取消</el-button>
          </div>
        </el-form>
      </div>
    </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  computed: {
    isEdit() {
      return !!this.$route.params.id
    }
  },
  data() {
    return {
      form: {
        name: '',
        cellphone: '',
        roleIds: []
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        cellphone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        roleIds: [{ required: true, message: '请选择角色', trigger: 'change' }]
      },
      roleOptions: [],
      submitting: false,
      pageLoading: true
    }
  },
  async created() {
    try {
      await this.loadRoles()
      if (this.isEdit) {
        const { name, cellphone, roleIds } = this.$route.query
        this.form.name = name
        this.form.cellphone = cellphone
        if (roleIds) {
          this.form.roleIds = roleIds.split(',').map(id => id * 1)
        }
      }
    } finally {
      this.pageLoading = false
    }
  },
  methods: {
    async loadRoles() {
      var roles = {
        offset: 0,
        limit: 1000,
        withTotal: true,
        filters: {
          name: '',
          disabled: null
        }
      }
      const [err, r] = await client.customerGetRoles({ body: roles })
      if (err) {
        handleError(err)
        return
      }
      this.roleOptions = r.data.list || []
    },

    handlePhoneInput() {
      // 只允许输入数字
      this.form.cellphone = this.form.cellphone.replace(/[^\d]/g, '')
    },

    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true

      try {
        if (this.isEdit) {
          const payload = {
            ...this.form,
            merberId: this.$route.params.id
          }
          const [err] = await client.customerEditMember({ body: payload })
          if (err) {
            handleError(err)
            return
          }
          this.$message.success('更新成功')
        } else {
          const [err] = await client.customerAddMember({ body: this.form })
          if (err) {
            handleError(err)
            return
          }
          this.$message.success('创建成功')
        }

        this.$router.push('/members')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.merchant-users-new-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 900px;
  min-height: 500px;
}

.user-form {
  width: 100%;
}

.form-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input,
.el-select {
  width: 100%;
}

.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .merchant-users-new-container {
    padding: 20px 12px;
  }

  .form-container {
    padding: 24px;
  }

  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
