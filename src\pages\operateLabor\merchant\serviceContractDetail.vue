<template>
  <div class="contract-detail-container" v-loading="loading" element-loading-text="加载中...">
    <div class="detail-container">
      <!-- 基本信息 -->
      <div class="summary-container">
        <div class="summary-title">基本信息</div>
        <div class="summary-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <label>合同名称：</label>
                <span>{{ contractData.name || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>作业主体：</label>
                <span>{{ contractData.supplierName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>合同有效期：</label>
                <span>{{ contractData.contractPeriod || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 发票信息 -->
      <div class="summary-container">
        <div class="summary-title">发票信息</div>
        <div class="summary-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <label>发票抬头：</label>
                <span>{{ contractData.invoiceTitle || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>纳税人识别号：</label>
                <span>{{ contractData.invoiceTaxNo || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>开户银行：</label>
                <span>{{ contractData.invoiceBankName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>银行账号：</label>
                <span>{{ contractData.invoiceBankAccount || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>注册地址：</label>
                <span>{{ contractData.invoiceRegisterAddress || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <label>联系电话：</label>
                <span>{{ contractData.invoiceCompanyTel || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="summary-item remark-item">
                <label>发票备注：</label>
                <span class="remark-content">{{ contractData.invoiceRemark || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 管理费计算 -->
      <div class="summary-container">
        <div class="summary-title">管理费计算</div>
        <div class="summary-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <label>计算规则：</label>
                <span>{{ getCalculationRuleText(contractData.manageCalculationRule) || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="contractData.manageCalculationRule === 'EMPLOYEE_COUNT'">
              <div class="summary-item">
                <label>服务费金额：</label>
                <span>{{ contractData.manageAmount ? `${contractData.manageAmount}元/人月` : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="contractData.manageCalculationRule === 'PAYABLE_AMOUNT_RATE'">
              <div class="summary-item">
                <label>费率：</label>
                <span>{{ contractData.manageRate ? `${contractData.manageRate}%` : '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 合同附件 -->
      <div class="summary-container">
        <div class="summary-title">合同附件</div>
        <div class="file-list-wrapper">
          <EnhancedFileList :fileIds="contractData.fileIds" />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="onBack" size="medium">
          返回
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import EnhancedFileList from './components/EnhancedFileList.vue'

const client = makeClient()

export default {
  components: { EnhancedFileList },
  data() {
    return {
      loading: true,
      contractId: null,
      contractData: {},
      rules: {}
    }
  },

  async created() {
    // 获取路由参数中的合同ID
    this.contractId = this.$route.params.id

    // 加载合同数据
    await this.loadContractData()
  },

  methods: {
    // 加载合同数据
    async loadContractData() {
      try {
        const [err, response] = await client.customerQueryContract({
          body: { id: this.contractId }
        })

        if(err){
          this.$message.error(err.message)
          this.loading = false
          return
        }
        if (response.success) {
          this.contractData = response.data || {}
          // 格式化合同有效期显示
          if (this.contractData.startDate && this.contractData.endDate) {
            this.contractData.contractPeriod = `${this.contractData.startDate} 至 ${this.contractData.endDate}`
          }
        } else {
          this.$message.error(response.message || '获取合同信息失败')
        }
        this.loading = false
      } catch (error) {
        handleError(error)
        this.loading = false
      }
    },

    // 获取计算规则文本
    getCalculationRuleText(rule) {
      const ruleMap = {
        'EMPLOYEE_COUNT': '按雇员人数计算',
        'PAYABLE_AMOUNT_RATE': '按应发金额比例计算'
      }
      return ruleMap[rule] || rule
    },

    getStatusText(status) {
      const statusMap = {
        INIT: '服务中',
        TERMINATION: '提前终止',
        EXPIRED: '已到期'
      }
      return statusMap[status] || status
    },

    getStatusTagType(status) {
      const typeMap = {
        INIT: 'success',
        TERMINATION: 'danger',
        EXPIRED: 'info'
      }
      return typeMap[status] || 'info'
    },

    // 返回
    onBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.contract-detail-container {
  overflow-y: auto;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  padding: 12px;
  box-sizing: border-box;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-container {
  background: #fff;
  border-radius: 8px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.summary-content {
  margin-top: 20px;
}

.summary-item {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 32px;
  display: flex;
  align-items: flex-start;
}

.summary-item label {
  display: inline-block;
  width: 120px;
  text-align: right;
  margin-right: 8px;
  color: #909399;
  flex-shrink: 0;
}

.summary-item span {
  color: #606266;
  word-break: break-all;
}

.remark-item {
  align-items: flex-start;
}

.form-actions {
  text-align: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 文件列表包装器 */
.file-list-wrapper {
  margin-top: 8px;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .detail-container {
    max-width: 100%;
    margin: 0;
  }
}

@media screen and (max-width: 768px) {
  .contract-detail-container {
    padding: 8px;
  }

  .summary-container {
    padding: 16px;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .summary-item label {
    width: auto;
    text-align: left;
    margin-bottom: 4px;
  }

  .el-col {
    margin-bottom: 8px;
  }
}
</style>
