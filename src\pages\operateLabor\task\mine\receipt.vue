<template>
  <div
    class="receipt"
    style="background: #f7fbfd; height: 100vh; overflow: auto; padding: 0 20px"
  >
    <div style="margin: 20px 0 20px 20px">
      {{ showQueryMonth }}
      <Icon @click="showPicker = true" name="arrow-down" />
    </div>
    <Popup v-model="showPicker" position="bottom">
      <DatetimePicker
        v-model="queryMonth"
        type="year-month"
        title="选择年月"
        :formatter="formatter"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </Popup>

    <Popup
      style="background: #f7fbfd"
      v-model="show"
      position="bottom"
      :style="{ height: '35%' }"
    >
      <div style="margin: 20px">
        <Cell
          style="border-radius: 10px 10px 0 0"
          title="实发金额"
          :value="detailForm.payableAmount"
        />
        <Cell
          title="本次应预扣预缴税额"
          :value="detailForm.currentTaxWithholding"
        />
        <Cell title="增值税额" :value="detailForm.vatAmount" />
        <Cell
          style="border-radius: 0 0 10px 10px"
          title="增值附加税额"
          :value="detailForm.additionalTaxAmount"
        />
      </div>
    </Popup>

    <div v-if="list.length">
      <div
        class="item"
        v-for="(item, index) in list"
        :key="index"
        @click="toDetail(item)"
      >
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          "
        >
          <span>{{ item.contractName }}</span>
          <span style="background: #fafbff; padding: 5px; color: #9294a0"
            >总发放：<span style="color: #9a3a42">{{ item.amount }}</span></span
          >
        </div>
        <div style="color: #aaaaaa; font-size: 12px">
          {{ item.completeTime }}
        </div>
      </div>
    </div>
    <div
      v-else
      style="
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 150px;
      "
    >
      <img width="180" src="kit/assets/images/no-data.png" alt="" />
      <div style="text-align: center; margin-top: 20px">暂无数据</div>
    </div>
  </div>
</template>
<script>
import { Icon, Popup, DatetimePicker, Cell } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

function getCurrentMonth() {
  const date = new Date()
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

export default {
  components: {
    Icon,
    Popup,
    DatetimePicker,
    Cell
  },
  data() {
    return {
      showPicker: false,
      show: false,
      showQueryMonth: getCurrentMonth(),
      queryMonth: getCurrentMonth(),
      list: [],
      detailForm: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatter(type, val) {
      if (type === 'year') {
        return val + '年'
      }
      if (type === 'month') {
        return val + '月'
      }
      return val
    },
    async getList() {
      const [err, r] = await client.apiProxyListProxy({
        body: {
          // start: 0,
          offset: 0,
          limit: 10,
          // sorts: [
          //   {
          //     field: '',
          //     direction: ''
          //   }
          // ],
          withTotal: true,
          withDisabled: true,
          withDeleted: true,
          filters: {
            corporationId: 1,
            queryMonth: this.queryMonth
          }
        }
      })
      if (err) return handleError(err)
      this.list = r.data.list
    },
    onConfirm(value) {
      this.queryMonth = this.formatDate(value)
      this.showQueryMonth = this.formatDate(value)
      this.getList()
      this.showPicker = false
    },
    formatDate(date) {
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`
    },
    toDetail(item) {
      this.detailForm = { ...item }
      this.show = true
    }
  }
}
</script>
<style scoped>
.item {
  background: #ffffff;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
}
</style>
