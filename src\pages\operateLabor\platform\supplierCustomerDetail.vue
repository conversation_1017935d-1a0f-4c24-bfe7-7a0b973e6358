<template>
  <div v-loading="loading">
    <!-- 客户名称和创建时间 -->
    <div style="background: white; padding: 10px; margin-bottom: 20px;">
      <h1 style="font-size: 25px; font-weight: 600; color: #262626; margin: 0 0 12px 0; line-height: 1.2;">
        {{ customerData.name }}
      </h1>
      <p style="font-size: 14px; color: #8c8c8c; margin: 0;">
        创建时间：{{ customerData.createTime }}
      </p>
    </div>

    <el-tabs  v-model="activeTab" type="card" style="padding: 10px">
      <el-tab-pane label="客户信息" name="customer" style="padding: 10px">
        <el-form   :model="customerData" label-width="180px" style="width: 800px;margin-left: 50px;" >

          <Title title="基本信息" />

          <el-form-item label="营业执照照片">
            <div style="display: inline-flex; flex-direction: column; align-items: center;">
              <ImagerUploader
                v-model="customerData.businessLicenseImage"
                :max="1"
                :width="200"
                :height="280"
                accept=".jpg,.jpeg,.png"
                name="营业执照"
                :disabled="true"
              />
            </div>
          </el-form-item>

          <el-form-item label="公司名称">
            <el-input v-model="customerData.shortName" readonly></el-input>
          </el-form-item>

          <el-form-item label="统一社会信用代码">
            <el-input v-model="customerData.socialCreditCode" readonly></el-input>
          </el-form-item>

          <el-form-item label="公司注册地址">
            <el-input v-model="customerData.address" readonly></el-input>
          </el-form-item>

          <Title title="法人信息" />

          <el-form-item label="法人身份证人像面">
            <div style="display: inline-flex; flex-direction: column; align-items: center;">
              <ImagerUploader
                v-model="customerData.certificateFrontImage"
                :max="1"
                :width="230"
                :height="170"
                name="上传人像面"
                :disabled="true"
              />
            </div>
          </el-form-item>

          <el-form-item label="法人身份证国徽面">
            <div style="display: inline-flex; flex-direction: column; align-items: center;">
              <ImagerUploader
                v-model="customerData.certificateBackImage"
                :max="1"
                :width="230"
                :height="170"
                name="上传国徽面"
                :disabled="true"
              />
            </div>
          </el-form-item>

          <el-form-item label="法人姓名">
            <el-input v-model="customerData.representativeName" readonly></el-input>
          </el-form-item>

          <el-form-item label="法人证件类型">
            <el-input :value="getCertificateTypeText(customerData.certificateType)" readonly></el-input>
          </el-form-item>

          <el-form-item label="法人证件号">
            <el-input v-model="customerData.certificateNo" readonly></el-input>
          </el-form-item>

          <Title title="联系人信息" />

          <el-form-item label="联系人姓名">
            <el-input v-model="customerData.enterpriseContacts" readonly></el-input>
          </el-form-item>

          <el-form-item label="联系人手机号">
            <el-input v-model="customerData.enterpriseContactPhone" readonly></el-input>
          </el-form-item>

          <Title title="管理员信息" />

          <el-form-item label="管理员姓名">
            <el-input v-model="customerData.adminName" readonly></el-input>
          </el-form-item>

          <el-form-item label="管理员手机号">
            <el-input v-model="customerData.adminMobile" readonly></el-input>
          </el-form-item>

          <Title title="操作角色" />

          <el-form-item label="操作角色">
            <div >
              <el-tag
                v-for="roleName in getSelectedRoleNames()"
                :key="roleName"
                type="primary"
                size="small"
                style="margin-right: 8px; margin-bottom: 4px;"
              >
                {{ roleName }}
              </el-tag>
              <span v-if="getSelectedRoleNames().length === 0" style="color: #999;">暂无角色</span>
            </div>
          </el-form-item>

        </el-form>
      </el-tab-pane>

      <el-tab-pane label="合同信息" name="contract">
        <div style="padding: 10px;">
          <el-table
            v-loading="contractLoading"
            :data="contractData"
            style="width: 100%; margin-top: 20px;"
            :header-cell-style="{
              'font-size': '12px',
              'font-weight': '400',
              color: '#777c94',
              background: 'var(--o-primary-bg-color)'
            }"
          >
            <el-table-column
              prop="id"
              label="合同编号"
              width="120"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="name"
              label="合同名称"
              width="250"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="customerName"
              label="客户"
              width="250"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="supplierName"
              label="作业主体"
              width="250"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="startDate"
              label="开始时间"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="endDate"
              label="结束时间"
              width="120"
            ></el-table-column>
            <el-table-column prop="createTime" width="200" label="创建日期"></el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">
                <span :class="['status-tag', getContractStatusClass(scope.row.status)]">
                  {{ getContractStatusText(scope.row.status) }}
                </span>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="contractTotal > 0"
            @current-change="handleContractCurrentChange"
            :current-page="contractConditions.offset / contractConditions.limit + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="contractConditions.limit"
            layout="total, prev, pager, next"
            :total="contractTotal"
            style="text-align: right; margin-top: 10px"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import ImagerUploader from './uploader/image.vue'

const client = makeClient()

export default {
  components: { Title, ImagerUploader },
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 1000,
        sorts: [
        ],
        withTotal: false,
        withDisabled: false,
        withDeleted: false,
        filters: {
          name: ''
        }
      },
      loading:true,
      activeTab: 'customer',
      customerId: null,
      customerData: {}, // 原始客户数据

      // 角色选项
      roleOptions: [],

      // 合同相关数据
      contractLoading: false,
      contractData: [],
      contractTotal: 0,
      contractConditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          customerId: ''
        }
      }
    }
  },

  watch: {
    // 监听标签页切换
    activeTab(newTab) {
      if (newTab === 'contract' && this.contractData.length === 0) {
        this.loadContractData()
      }
    }
  },

  async created() {
    // 获取路由参数中的客户ID
    this.customerId = this.$route.params.id
    this.contractConditions.filters.customerId = this.customerId

    // 加载数据
    await this.loadRoleOptions()
    await this.loadCustomerData()

  },

  methods: {
    // 加载客户数据
    async loadCustomerData() {
      try {
        const [err, response] = await client.queryCustomer({
          body: { id: this.customerId }
        })
        console.log('response', response)

        if (response.success) {
          this.customerData = response.data || {}
        } else {
          this.$message.error(response.message || '获取客户信息失败')
        }
        this.loading = false
      } catch (error) {
        handleError(error)
      }
    },
    async loadRoleOptions() {
      try {
        const [err, response] = await client.listRoles({
          body: this.conditions
        })

        console.log('listRole接口返回结果：', response)

        if (response.success && response.data) {
          console.log('角色数据：', response.data)
          this.roleOptions = response.data.list || response.data || []
          console.log('设置的roleOptions：', this.roleOptions)
        } else {
          console.error('获取角色列表失败：', response.message)
          this.$message.error(response.message || '获取角色列表失败')
        }
      } catch (error) {
        console.error('加载角色选项异常：', error)
        this.$message.error('加载角色选项失败')
      }
    },

    // 获取证件类型文本
    getCertificateTypeText(type) {
      const typeMap = {
        'ID_CARD': '居民身份证',
        'PASSPORT': '护照',
        'HONG_KONG_MACAO_TAIWAN': '港澳台同胞回乡证',
        'FOREIGNER': '外国人永久居留证'
      }
      return typeMap[type] || '居民身份证'
    },

    // 获取选中角色的名称列表
    getSelectedRoleNames() {
      if (!this.customerData.roleIds || !Array.isArray(this.customerData.roleIds)) {
        return []
      }
      return this.customerData.roleIds
        .map(roleId => {
          const role = this.roleOptions.find(option => option.id === roleId)
          return role ? role.name : null
        })
        .filter(name => name !== null)
    },

    // 加载合同数据
    async loadContractData() {
      this.contractLoading = true

      try {
        const [err, r] = await client.supplierListContract({
          body: this.contractConditions
        })

        this.contractLoading = false

        if (err) {
          handleError(err)
          return
        }

        this.contractData = r.data.list || []
        this.contractTotal = r.data.total || 0
      } catch (error) {
        this.contractLoading = false
        handleError(error)
      }
    },

    // 合同分页处理
    handleContractCurrentChange(page) {
      this.contractConditions.offset = (page - 1) * this.contractConditions.limit
      this.loadContractData()
    },

    // 获取合同状态文本
    getContractStatusText(status) {
      const statusMap = {
        'INIT': '服务中',
        'TERMINATION': '提前终止',
        'EXPIRED': '已到期'
      }
      return statusMap[status] || status
    },

    // 获取合同状态样式类
    getContractStatusClass(status) {
      const classMap = {
        'INIT': 'status-ongoing',
        'TERMINATION': 'status-stopped',
        'EXPIRED': 'status-negotiation'
      }
      return classMap[status] || 'status-default'
    },

    // 新增合同
    handleAddContract() {
      this.$router.push(`/serviceContracts/new?customerId=${this.customerId}`)
    }
  }
}
</script>

<style scoped>
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.status-ongoing {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-stopped {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-negotiation {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-default {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}
</style>