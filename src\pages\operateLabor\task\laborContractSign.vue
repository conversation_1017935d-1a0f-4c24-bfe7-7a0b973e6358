
<template>
  <div class="labor-contract-sign">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="协议合同签署"
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
    />

    <!-- 签名区域 -->
    <div class="signature-section">
      <div class="signature-title">请在下方签名</div>

      <!-- 签名画板 -->
      <div class="signature-container">
        <canvas
          ref="signatureCanvas"
          class="signature-canvas"
          @touchstart="startDrawing"
          @touchmove="draw"
          @touchend="stopDrawing"
          @mousedown="startDrawing"
          @mousemove="draw"
          @mouseup="stopDrawing"
        ></canvas>

        <!-- 签名预览 -->
        <div v-if="signatureImage" class="signature-preview">
          <img :src="signatureImage" alt="签名预览" />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="signature-actions">
        <van-button
          type="default"
          size="large"
          @click="clearSignature"
        >
          清除
        </van-button>
        <van-button
          type="primary"
          size="large"
          :disabled="!hasSignature"
          :loading="loading"
          @click="saveSignature"
        >
          授权
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { NavBar, Button, Dialog, Loading, Toast } from 'vant'
import { delay } from 'kit/helpers/delay'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'LaborContractSign',
  components: {
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [Dialog.name]: Dialog,
    [Loading.name]: Loading
  },
  data() {
    return {
      protocolId: '',
      isDrawing: false,
      canvas: null,
      ctx: null,
      signatureImage: '', // 用于显示的完整dataURL
      signatureBase64: '', // 用于提交的纯base64数据
      hasSignature: false,
      lastX: 0,
      lastY: 0,
      loading: false
    }
  },
  async created() {
    this.protocolId = this.$route.query.protocolId
    if (!this.protocolId) {
      Toast.fail('缺少合同ID')
      this.$router.go(-1)
      return
    }
  },
  mounted() {
    this.initCanvas()
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1)
    },
    initCanvas() {
      this.canvas = this.$refs.signatureCanvas
      this.ctx = this.canvas.getContext('2d')

      // 设置画布尺寸
      const container = this.canvas.parentElement
      this.canvas.width = container.clientWidth
      this.canvas.height = 200

      // 设置画笔样式
      this.ctx.strokeStyle = '#000'
      this.ctx.lineWidth = 2
      this.ctx.lineCap = 'round'
      this.ctx.lineJoin = 'round'
    },

    // 获取触摸/鼠标位置
    getPosition(event) {
      const rect = this.canvas.getBoundingClientRect()
      const clientX = event.touches ? event.touches[0].clientX : event.clientX
      const clientY = event.touches ? event.touches[0].clientY : event.clientY

      return {
        x: clientX - rect.left,
        y: clientY - rect.top
      }
    },

    // 开始绘制
    startDrawing(event) {
      event.preventDefault()
      this.isDrawing = true
      const pos = this.getPosition(event)
      this.lastX = pos.x
      this.lastY = pos.y

      this.ctx.beginPath()
      this.ctx.moveTo(pos.x, pos.y)
    },

    // 绘制
    draw(event) {
      if (!this.isDrawing) return
      event.preventDefault()

      const pos = this.getPosition(event)
      this.ctx.lineTo(pos.x, pos.y)
      this.ctx.stroke()

      this.lastX = pos.x
      this.lastY = pos.y
      this.hasSignature = true
    },

    // 停止绘制
    stopDrawing(event) {
      if (!this.isDrawing) return
      event.preventDefault()
      this.isDrawing = false
    },

    clearSignature() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
      this.signatureImage = ''
      this.signatureBase64 = ''
      this.hasSignature = false
    },

    saveSignature() {
      if (!this.hasSignature) {
        Toast.fail('请先进行签名')
        return
      }

      // 获取canvas的base64数据
      const dataURL = this.canvas.toDataURL('image/png')
      // 保存完整的dataURL用于显示
      this.signatureImage = dataURL
      // 移除前缀，只保留纯base64数据用于提交
      this.signatureBase64 = dataURL.replace(/^data:image\/png;base64,/, '')
      this.confirmSign()
    },

    // 确认签署
    async confirmSign() {
      this.loading = true
      await delay(2000)

      try {
        const [err] = await client.supplierProtocolSign({
          body: {
            protocolId: this.protocolId,
            signImage: this.signatureBase64
          }
        })

        if (err) {
          handleError(err)
          this.loading = false
          return
        }

        Toast.success('签署成功')
        setTimeout(() => {
          this.$router.push({
            path: '/laborContract'
          })
        }, 1000)

      } catch (error) {
        Toast.fail('签署失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.labor-contract-sign {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.contract-info {
  padding: 15px 20px;
  background: #fff;
  margin: 10px;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 14px;
  min-width: 80px;
}

.value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.signature-section {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.signature-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.signature-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.signature-canvas {
  width: 100%;
  height: 200px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  cursor: crosshair;
  touch-action: none;
}

.signature-preview {
  margin-top: 15px;
  text-align: center;
}

.signature-preview img {
  max-width: 100%;
  max-height: 100px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.signature-actions {
  display: flex;
  gap: 15px;
}

.signature-actions .van-button {
  flex: 1;
}

.bottom-actions {
  padding: 15px 20px;
  background: #fff;
  border-top: 1px solid #eee;
}

.sign-dialog-content {
  padding: 20px;
  text-align: center;
}

.sign-dialog-content p {
  margin: 10px 0;
  color: #666;
}

.contract-name {
  font-weight: bold;
  color: #333;
}

.dialog-signature {
  margin-top: 15px;
}

.dialog-signature img {
  max-width: 200px;
  max-height: 60px;
  border: 1px solid #eee;
  border-radius: 4px;
}
</style>

