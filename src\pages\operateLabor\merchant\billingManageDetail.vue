<template>
  <div class="billing-detail-container" v-loading="loading" element-loading-text="加载中...">
    <div class="detail-container">
      <!-- 账单基本信息 -->
      <div class="info-header">
        <div class="bill-month-card">
          <div class="month-display">{{ formatterMonth(info.billMonth) }}</div>
          <div class="month-label">账单月份</div>
        </div>
        <div class="bill-info">
          <div class="info-item">
            <span class="label">客户名称：</span>
            <span class="value">{{ info.customerName }}</span>
          </div>
          <div class="info-item">
            <span class="label">服务合同：</span>
            <span class="value">{{ info.contractName }}</span>
          </div>
        </div>
        <div class="total-amount">
          <span class="amount-label">总费用：</span>
          <span class="amount-value">￥{{ info.totalReceivableAmount || '0.00' }}</span>
        </div>
      </div>

      <!-- 费用项目列表 -->
      <div class="table-container">

        <el-table
          :data="tableData"
          :height="tableHeight"
          :stripe="false"
          highlight-current-row
          :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        >
          <template slot="empty">
            <div class="empty-data">暂无数据</div>
          </template>
          <el-table-column
            prop="feeTypeDesc"
            label="结算项目"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="billMonth"
            label="结算月份"
            min-width="200"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="personCount"
            label="办理人数"
            min-width="180"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="totalAmount"
            label="应收金额"
            min-width="160"
          ></el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleView(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>


      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      loading: true,
      tableHeight: 500,
      info: {
        billMonth: '',
        customerName: '',
        contractName: '',
        supplierCorporationName: '',
        totalReceivableAmount: ''
      },
      tableData: []
    }
  },
  async created() {
    await this.loadDetail()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadDetail() {
      this.loading = true
      const billId = this.$route.params.id

      try {
        // 获取账单详情
        const [err, response] = await client.apiCustomerBillsDetail({
          body: { billId }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.info = response.data
          this.tableData = response.data.categories || []
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
        this.$nextTick(() => {
          this.setTableHeight()
        })
      }
    },

    formatterMonth(value) {
      if (!value) return ''
      return value.substring(0, 7) // yyyy-MM格式
    },

    handleView(row) {
      // 根据费用类型跳转到不同的详情页面
      const { billMasterId, feeType } = row
      if (feeType === 'SALARY') {
        this.$router.push({
          path: '/billingManage/detail/salary',
          query: {
            billMasterId: billMasterId,
            contractName: this.info.contractName,
            rowData: JSON.stringify(row)
          }
        })
      } else if (feeType === 'MANAGEMENT_FEE') {
        this.$router.push({
          path: '/billingManage/detail/managementFee',
          query: {
            billMasterId: billMasterId,
            contractName: this.info.contractName,
            rowData: JSON.stringify(row)
          }
        })
      } else {
        this.$router.push({
          path: '/billingManage/detail/otherFee',
          query: {
            billMasterId: billMasterId,
            contractName: this.info.contractName,
            rowData: JSON.stringify(row)
          }
        })
      }
    },

    setTableHeight() {
      const windowHeight = window.innerHeight
      const infoHeader = this.$el.querySelector('.info-header')
      const tableHeader = this.$el.querySelector('.table-header')
      const headerHeight = (infoHeader ? infoHeader.offsetHeight : 0) + (tableHeader ? tableHeader.offsetHeight : 0)
      const padding = 60

      const availableHeight = windowHeight - headerHeight - padding
      const minHeight = 300
      const maxHeight = windowHeight - headerHeight - padding - 20

      this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight))
      this.tableHeight = Math.floor(this.tableHeight)
    }
  }
}
</script>

<style scoped>
.billing-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.info-header {
  background: #fff;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bill-month-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.month-display {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.month-label {
  font-size: 12px;
  opacity: 0.9;
}

.bill-info {
  flex: 1;
  margin-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.value {
  color: #303133;
  font-weight: 500;
}

.total-amount {
  display: flex;
  align-items: flex-end;
}

.amount-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 24px;
  font-weight: 600;
  color: #E6A23C;
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .billing-detail-container {
    padding: 12px;
  }

  .info-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .bill-info {
    margin-left: 0;
    width: 100%;
  }

  .total-amount {
    align-items: flex-start;
    width: 100%;
  }

  .table-container {
    padding: 12px;
  }
}
</style>
