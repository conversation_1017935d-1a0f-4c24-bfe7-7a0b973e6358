<template>
  <div class="billing-manage-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
             <el-form-item>
              <el-select
                v-model="conditions.filters.contractId"
                placeholder="请选择服务合同"
                clearable
                filterable
              >
                <el-option
                  v-for="item in contractOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="conditions.filters.billMonth"
                type="month"
                placeholder="请选择账单月"
                format="yyyy-MM"
                value-format="yyyy-MM"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="conditions.filters.billStatus"
                placeholder="请选择状态"
                clearable
              >
                <el-option label="待确认" value="PENDING_CONFIRM"></el-option>
                <el-option label="已确认" value="CONFIRMED"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </div>
        </div>
      </el-form>
    </div>


    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        row-key="id"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column
          prop="id"
          label="结算账单ID"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="contractName"
          label="服务合同名称"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="supplierCorporationName"
          label="作业主体名称"
          min-width="160"
        ></el-table-column>
        <el-table-column
          prop="totalReceivableAmount"
          label="总费用"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="billMonth"
          label="账单月"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="生成时间"
          width="160"
          :formatter="formatDateTime"
        ></el-table-column>
        <el-table-column
          prop="confirmTime"
          label="确认时间"
          width="160"
          :formatter="formatDateTime"
        ></el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.billStatus)"
              size="small"
            >
              {{ formatterStatus(scope.row.billStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" min-width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button
              v-if="scope.row.billStatus === 'PENDING_CONFIRM'"
              type="text"
              size="small"
              @click="handleConfirm(scope.row)"
            >
              确认
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          contractId: '',
          billMonth: '',
          billStatus: null
        }
      },
      total: 0,
      tableData: [],
      loading: true,
      tableHeight: 500,
      contractOptions: []
    }
  },
  async created() {
    await this.loadContractOptions()
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    // 加载合同选项
    async loadContractOptions() {
      try {
        const [err, response] = await client.customerGetAllContracts({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.contractOptions = response.data || []
        }
      } catch (error) {
        console.error('加载合同选项失败：', error)
      }
    },

    async getList() {
      this.loading = true

      // 处理账单月份，转换为开始和结束日期
      const requestConditions = { ...this.conditions }
      if (this.conditions.filters.billMonth) {
        const billMonth = this.conditions.filters.billMonth
        requestConditions.filters.billMonthStart = `${billMonth}-01`
        requestConditions.filters.billMonthEnd = `${billMonth}-01`
      }

      const [err, r] = await client.apiCustomerBillsList({
        body: requestConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions.filters = {
        contractId: '',
        billMonth: '',
        billStatus: null
      }
      this.onSearch()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    formatterStatus(value) {
      switch (value) {
        case 'GENERATING':
          return '生成中'
        case 'GENERATED':
          return '已生成'
        case 'PENDING_CONFIRM':
          return '待确认'
        case 'CONFIRMED':
          return '已确认'
        default:
          return '-'
      }
    },

    getStatusTagType(status) {
      const typeMap = {
        'GENERATING': 'warning',
        'GENERATED': 'info',
        'PENDING_CONFIRM': 'warning',
        'CONFIRMED': 'success'
      }
      return typeMap[status] || 'info'
    },

    handleView(row) {
      this.$router.push(`/billingManage/${row.id}`)
    },

    async handleConfirm(row) {
      try {
        await this.$confirm(`确定要确认账单吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const [err] = await client.apiCustomerBillsConfirm({
          body: {
            billId: row.id
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('确认成功')
        this.getList()
      } catch (error) {
        // 用户取消确认，不做任何操作
      }
    },

    setTableHeight() {
      const windowHeight = window.innerHeight;
      const searchContainer = this.$el.querySelector('.search-container');
      const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
      const pagination = this.$el.querySelector('.pagination-container');
      const paginationHeight = pagination ? pagination.offsetHeight : 40;
      const padding = 40;

      const availableHeight = windowHeight - searchHeight - paginationHeight - padding;

      if (this.tableData.length <= 5) {
        this.tableHeight = 500;
      } else {
        const minHeight = 300;
        const maxHeight = windowHeight - searchHeight - paginationHeight - padding - 5;
        this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
      }

      this.tableHeight = Math.floor(this.tableHeight);
    }
  }
}
</script>

<style scoped>
.billing-manage-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 200px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.search-main .el-form-item .el-form-item__content {
  width: 200px;
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.search-actions .el-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.search-actions .toggle-btn {
  color: #409eff;
  padding: 8px 4px;
}

.el-form-item .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-form-item .el-input,
.el-form-item .el-select,
.el-form-item .el-date-editor {
  width: 100%;
}

.more-form-items {
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.more-form-items .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.more-form-items .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.more-form-items .el-form-item .el-form-item__content {
  width: 300px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.more-form-items .el-form-item {
  width: calc(50% - 20px);
  margin-right: 20px;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.more-form-items .el-date-editor.el-input__inner,
.more-form-items .el-date-editor.el-input,
.more-form-items .el-date-editor.el-range-editor {
  width: 100%;
  min-width: 300px;
}

.toggle-btn {
  padding: 0;
  font-size: 14px;
  margin-left: 16px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
}

.toggle-btn:hover {
  transform: translateY(-1px);
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 */
@media screen and (max-width: 1400px) {
  .more-form-items .el-form-item {
    width: calc(50% - 20px);
  }
}

@media screen and (max-width: 1200px) {
  .search-header {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .search-main {
    flex: 1;
    min-width: 70%;
    margin-bottom: 16px;
  }

  .search-actions {
    justify-content: flex-end;
    min-width: 200px;
  }

  .more-form-items .el-form-item {
    width: calc(100% - 20px);
  }
}

@media screen and (max-width: 992px) {
  .search-main {
    flex-wrap: wrap;
  }

  .search-main .el-form-item {
    width: calc(50% - 20px);
    margin-bottom: 16px;
  }
}

/* 响应式调整 */
@media screen and (max-width: 1400px) {
  .search-main {
    margin-right: 180px;
  }

  .more-form-items .el-form-item {
    width: calc(33.33% - 20px);
  }
}

@media screen and (max-width: 1200px) {
  .search-main {
    margin-right: 160px;
  }

  .search-main .el-form-item {
    min-width: 260px;
  }

  .more-form-items .el-form-item {
    width: calc(50% - 20px);
  }
}

@media screen and (max-width: 992px) {
  .search-main {
    margin-right: 140px;
  }

  .search-main .el-form-item {
    min-width: 240px;
    margin-bottom: 12px;
  }

  .search-main .el-form-item .el-input,
  .search-main .el-form-item .el-select {
    width: 200px;
  }
}

@media screen and (max-width: 768px) {
  .search-header {
    flex-direction: column;
  }

  .search-main {
    margin-right: 0;
    margin-bottom: 16px;
    width: 100%;
  }

  .search-main .el-form-item {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
    min-width: auto;
  }

  .search-main .el-form-item .el-input,
  .search-main .el-form-item .el-select {
    width: 100%;
  }

  .search-actions {
    position: static;
    justify-content: flex-start;
    width: 100%;
  }

  .search-actions .el-button {
    margin-left: 0;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .more-form-items .el-form-item {
    width: 100%;
    margin-right: 0;
  }

  .more-form-items .el-date-editor.el-input__inner,
  .more-form-items .el-date-editor.el-input,
  .more-form-items .el-date-editor.el-range-editor {
    width: 100%;
    min-width: auto;
  }
}

@media screen and (max-width: 480px) {
  .search-actions {
    flex-wrap: wrap;
  }

  .search-actions .el-button {
    flex: 1;
    min-width: 60px;
    margin-bottom: 8px;
  }
}
</style>
