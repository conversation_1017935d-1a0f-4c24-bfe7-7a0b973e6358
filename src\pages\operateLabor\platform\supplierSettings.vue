<template>
  <div class="supplier-settings">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      style="width: 800px"
    >
      <!-- 基本信息 -->
      <Title title="基本信息" />
      <el-form-item label="企业名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入企业名称"></el-input>
      </el-form-item>
      <el-form-item label="统一信用代码" prop="socialCreditCode">
        <el-input
          v-model="form.socialCreditCode"
          placeholder="请输入统一信用代码"
        ></el-input>
      </el-form-item>

      <!-- 品牌信息 -->
      <Title title="品牌信息" />
      <el-form-item label="品牌名称" prop="brandName">
        <el-input
          v-model="form.brandName"
          placeholder="请输入品牌名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="全域名" prop="domainName">
        <el-input
          v-model="form.domainName"
          placeholder="请输入全域名"
        ></el-input>
      </el-form-item>
      <el-form-item label="企业 logo" prop="logoUrl">
        <ImageUploader
          v-model="form.logoUrl"
          :max="1"
          name="上传企业logo"
          :width="120"
          :height="120"
        />
      </el-form-item>
      <el-form-item label="短信签名" prop="signatureCode">
        <el-input
          v-model="form.signatureCode"
          placeholder="请输入短信签名"
        ></el-input>
      </el-form-item>

      <!-- 灵工市场配置 -->
      <Title title="灵工市场配置" />
      <el-form-item label="登录页 logo" prop="h5LogoUrl">
        <ImageUploader
          v-model="form.h5LogoUrl"
          :max="1"
          name="上传登录页logo"
          :width="120"
          :height="120"
        />
      </el-form-item>
      <el-form-item label="H5域名" prop="h5DomainName">
        <el-input
          v-model="form.h5DomainName"
          placeholder="请输入H5域名"
        ></el-input>
      </el-form-item>
      <el-form-item label="banner" prop="banner">
        <ImageUploader
          v-model="form.banner"
          :max="5"
          :multi="true"
          name="上传banner"
          :width="120"
          :height="120"
        />
      </el-form-item>

      <el-form-item label="任务标签" class="task-tag-section">
        <div style="margin-bottom: 20px;">
          <el-row :gutter="10" style="margin-bottom: 15px; align-items: center;">
            <el-col :span="6">
              <el-input
                v-model="taskTagForm.name"
                placeholder="请输入标签名称"
              />
            </el-col>
            <el-col :span="5">
              <div class="upload-button-container" style="margin-top: 2px;">
                <el-button
                  @click="triggerFileUpload"
                  :loading="uploading"
                >
                <i class="el-icon-upload2" v-if="!uploading"></i>
                  {{ uploading ? '上传中' : '上传图标' }}
                </el-button>
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/*,.ico,.svg,.webp,.bmp,.tiff"
                  @change="handleFileUpload"
                  style="display: none;"
                />
              </div>
            </el-col>
            <el-col :span="4" v-if="taskTagForm.fileId">
              <div class="file-preview-container" style="margin-top: 2px;">
                <!-- 图片预览 -->
                <div v-if="isImageFile" class="image-preview">
                  <img
                    :src="getPreviewUrl(taskTagForm.fileId)"
                    @click="previewUploadedImage"
                    class="preview-thumbnail"
                    title="点击查看大图"
                  />
                </div>
                <!-- 图标文件预览 -->
                <div v-else class="icon-preview">
                  <i class="el-icon-document" style="font-size: 32px; color: #409EFF;"></i>
                  <div class="file-name">{{ uploadedFileName }}</div>
                </div>
                <el-button
                  type="text"
                  size="mini"
                  @click="removeUploadedImage"
                  class="remove-btn"
                  title="删除文件"
                >
                  ×
                </el-button>
              </div>
            </el-col>
            <el-col :span="4">
              <el-button
                type="primary"
                @click="addTaskTag"
                :loading="addingTaskTag"
              >
                {{ addingTaskTag ? '新增中...' : '新增' }}
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 任务标签列表 -->
        <div v-loading="taskTagListLoading" style="margin-bottom: 20px;">
          <div v-if="taskTagList.length === 0" style="color: #999; text-align: center; padding: 20px;">
            暂无任务标签
          </div>
          <div v-else style="display: flex; flex-wrap: wrap; gap: 10px;">
            <el-tag
              v-for="tag in taskTagList"
              :key="tag.id"
              size="medium"
              closable
              @close="deleteTaskTag(tag)"
              @click="showTaskTagDetail(tag)"
              style="cursor: pointer;"
            >
              {{ tag.description }}
            </el-tag>
          </div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="submitting">
          保存设置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 任务标签详情对话框 -->
    <el-dialog
      title="任务标签详情"
      :visible.sync="taskTagDetailVisible"
      width="450px"
      :close-on-click-modal="true"
    >
      <div v-loading="taskTagDetailLoading">
        <div v-if="currentTaskTag" class="task-tag-detail">
          <div class="detail-row">
            <span class="detail-label">标签名称：</span>
            <span class="detail-value">{{ currentTaskTag.description }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">标签图片：</span>
            <div class="detail-value">
              <div v-if="currentTaskTag.fileId" class="image-container">
                <img
                  :src="getPreviewUrl(currentTaskTag.fileId)"
                  :alt="currentTaskTag.description"
                  class="tag-image"
                  @click="previewTagImage"
                />
                <div class="image-tip">点击图片查看大图</div>
              </div>
              <div v-else class="no-image">暂无图片</div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="taskTagDetailVisible = false">关闭</el-button> -->
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="imagePreviewVisible"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="auto"
      custom-class="image-preview-dialog"
    >
      <img
        v-if="previewImageUrl"
        :src="previewImageUrl"
        style="max-width: 100%; max-height: 70vh"
      />
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import ImageUploader from './uploader/image.vue'

const client = makeClient()

export default {
  components: { Title, ImageUploader },

  data() {
    return {
      submitting: false,
      supplierData: null,
      form: {
        id: 0,
        supplierNo: '',
        name: '',
        socialCreditCode: '',
        contacts: '',
        contactPhone: '',
        signatureCode: '',
        domainName: '',
        slogan: '',
        logoUrl: '',
        brandName: '',
        h5DomainName: '',
        banner: [],
        h5LogoUrl: '',
        h5ServiceAgreement: [],
        attachments: [],
        disabled: false
      },

      // 任务标签相关数据
      taskTagForm: {
        name: '',
        fileId: ''
      },
      taskTagList: [],
      taskTagListLoading: false,
      addingTaskTag: false,
      taskTagDetailVisible: false,
      taskTagDetailLoading: false,
      currentTaskTag: null,
      imagePreviewVisible: false,
      previewImageUrl: '',

      // 上传相关状态
      uploading: false,
      uploadedFileName: '',
      rules: {
        name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
        socialCreditCode: [
          { required: true, message: '请输入统一信用代码', trigger: 'blur' }
        ],
        brandName: [
          { required: true, message: '请输入品牌名称', trigger: 'blur' }
        ],
        domainName: [
          { required: true, message: '请输入全域名', trigger: 'blur' }
        ],
        signatureCode: [
          { required: true, message: '请输入短信签名', trigger: 'blur' }
        ],
        h5DomainName: [
          { required: true, message: '请输入H5域名', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    // 判断是否为图片文件
    isImageFile() {
      if (!this.uploadedFileName) return false
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff']
      const fileName = this.uploadedFileName.toLowerCase()
      return imageExtensions.some(ext => fileName.endsWith(ext))
    }
  },

  async created() {
    await this.loadSupplierDetails()
    await this.loadTaskTagList()
  },

  methods: {
    // 加载供应商详情
    async loadSupplierDetails() {
      try {
        const [err, response] = await client.supplierDetail({ body: {} })

        if (err) {
          handleError(err)
          return
        }

        if (response.success && response.data) {
          this.supplierData = response.data
          this.populateForm(response.data)
        }
      } catch (error) {
        handleError(error)
      }
    },

    // 填充表单数据
    populateForm(data) {
      this.form.id = data.id || 0
      this.form.supplierNo = data.supplierNo || ''
      this.form.name = data.info?.name || ''
      this.form.socialCreditCode = data.info?.socialCreditCode || ''
      this.form.contacts = data.info?.contacts || ''
      this.form.contactPhone = data.info?.contactPhone || ''
      this.form.signatureCode = data.signatureCode || ''
      this.form.domainName = data.domain?.domainName || ''
      this.form.slogan = data.domain?.slogan || ''
      this.form.logoUrl = data.domain?.logoUrl || ''
      this.form.brandName = data.domain?.brandName || ''
      this.form.h5DomainName = data.domain?.h5DomainName || ''
      this.form.banner = data.domain?.banner || []
      this.form.h5LogoUrl = data.domain?.h5LogoUrl || ''
      this.form.h5ServiceAgreement = data.domain?.h5ServiceAgreement || []
      this.form.attachments = data.info?.attachments || []
      this.form.disabled = data.disabled || false
    },

    // 重置表单
    resetForm() {
      if (this.supplierData) {
        this.populateForm(this.supplierData)
      }
    },

    // 提交表单
    async onSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) {
          return
        }

        this.submitting = true

        const [err] = await client.editSupplier({ body: this.form })

        if (err) {
          handleError(err)
          return
        }

        this.$message.success('设置成功')

        // 重新加载设置
        this.loadSupplierDetails()
      } catch (error) {
      } finally {
        this.submitting = false
      }
    },

    // 加载任务标签列表
    async loadTaskTagList() {
      this.taskTagListLoading = true
      try {
        const [err, response] = await client.queryTaskTag({ body: {} })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.taskTagList = response.data || []
        } else {
          this.$message.error(response.message || '获取任务标签列表失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.taskTagListLoading = false
      }
    },

    // 新增任务标签
    async addTaskTag() {
      if (!this.taskTagForm.name.trim()) {
        this.$message.error('请输入标签名称')
        return
      }
      if (!this.taskTagForm.fileId) {
        this.$message.error('请上传图标')
        return
      }

      this.addingTaskTag = true
      try {
        const [err, response] = await client.addTaskTag({
          body: {
            name: this.taskTagForm.name.trim(),
            fileId: this.taskTagForm.fileId
          }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('新增任务标签成功')
          // 重置表单
          this.taskTagForm = {
            name: '',
            fileId: ''
          }
          this.uploadedFileName = ''
          // 刷新列表
          await this.loadTaskTagList()
        } else {
          this.$message.error(response.message || '新增任务标签失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.addingTaskTag = false
      }
    },

    // 删除任务标签
    async deleteTaskTag(tag) {
      try {
        await this.$confirm(`确定要删除标签"${tag.description}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 设置删除状态
        this.$set(tag, 'deleting', true)

        const [err, response] = await client.deleteTaskTag({
          body: { id: tag.id }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('删除任务标签成功')
          // 刷新列表
          await this.loadTaskTagList()
        } else {
          this.$message.error(response.message || '删除任务标签失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          handleError(error)
        }
      } finally {
        this.$set(tag, 'deleting', false)
      }
    },

    // 显示任务标签详情
    async showTaskTagDetail(tag) {
      // 防止频繁点击导致的图片错乱
      if (this.taskTagDetailLoading) {
        return
      }

      this.currentTaskTag = tag
      this.taskTagDetailVisible = true
      this.taskTagDetailLoading = true

      // 确保图片URL与当前标签匹配
      const currentTagId = tag.id
      setTimeout(() => {
        // 再次检查是否还是同一个标签，防止快速切换导致的错乱
        if (this.currentTaskTag && this.currentTaskTag.id === currentTagId) {
          this.taskTagDetailLoading = false
        }
      }, 500)
    },

    // 预览标签图片
    previewTagImage() {
      if (this.currentTaskTag && this.currentTaskTag.fileId) {
        this.previewImageUrl = this.getPreviewUrl(this.currentTaskTag.fileId)
        this.imagePreviewVisible = true
      }
    },

    // 触发文件上传
    triggerFileUpload() {
      this.$refs.fileInput.click()
    },

    // 处理文件上传
    async handleFileUpload(event) {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型 - 支持图片和图标文件
      const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'image/bmp', 'image/tiff', 'image/svg+xml', 'image/x-icon'
      ]
      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.svg', '.ico']

      const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
      const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension)

      if (!isValidType) {
        this.$message.error('请选择图片或图标文件（支持 jpg、png、gif、webp、bmp、tiff、svg、ico 格式）')
        return
      }

      // 验证文件大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.$message.error('文件大小不能超过5MB')
        return
      }

      this.uploading = true
      this.uploadedFileName = file.name

      try {
        const formData = new FormData()
        formData.append('file', file)

        const [err, response] = await client.uploadFile({
          body: formData
        })

        if (err) {
          throw new Error(err.message || '上传失败')
        }

        this.taskTagForm.fileId = response.data.fileId
        this.$message.success('文件上传成功')
      } catch (error) {
        this.$message.error(`文件上传失败: ${error.message}`)
        this.uploadedFileName = ''
        this.taskTagForm.fileId = ''
      } finally {
        this.uploading = false
        // 清空input值，允许重复选择同一文件
        event.target.value = ''
      }
    },

    // 移除已上传的图片
    removeUploadedImage() {
      this.taskTagForm.fileId = ''
      this.uploadedFileName = ''
    },

    // 预览已上传的图片
    previewUploadedImage() {
      if (this.taskTagForm.fileId) {
        this.previewImageUrl = this.getPreviewUrl(this.taskTagForm.fileId)
        this.imagePreviewVisible = true
      }
    },

    // 获取图片预览URL
    getPreviewUrl(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    }
  }
}
</script>

<style scoped>
.supplier-settings {
  padding: 20px;
}

.task-tag-section {
  margin-bottom: 20px;
}

.task-tag-section .el-tag {
  margin-right: 10px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.task-tag-section .el-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.disabled-link {
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}

/* 上传按钮容器样式 */
.upload-button-container {
  display: flex;
  align-items: center;
}

/* 文件预览容器样式 */
.file-preview-container {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

/* 图片预览样式 */
.image-preview {
  display: flex;
  align-items: center;
}

.preview-thumbnail {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.preview-thumbnail:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 图标文件预览样式 */
.icon-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.file-name {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 删除按钮样式 */
.remove-btn {
  color: #f56c6c;
  font-size: 16px;
  font-weight: bold;
  padding: 0;
  min-width: auto;
  height: auto;
  line-height: 1;
  margin-left: 4px;
}

.remove-btn:hover {
  color: #f78989;
}

/* 任务标签详情样式 */
.task-tag-detail {
  padding: 10px 0;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 80px;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
  line-height: 1.5;
}

.detail-value {
  flex: 1;
  color: #666;
  line-height: 1.5;
}

.tag-image {
  max-width: 200px;
  max-height: 200px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.tag-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.no-image {
  color: #999;
  font-style: italic;
}

/* 图片预览对话框样式 */
::v-deep .image-preview-dialog {
  text-align: center;
}

::v-deep .image-preview-dialog .el-dialog__body {
  padding: 20px;
}
</style>
