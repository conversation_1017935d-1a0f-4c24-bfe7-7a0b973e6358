<template>
  <el-dialog
    title="任务完成凭证"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="credentials-content" v-loading="loading">
      <div class="task-info">
        <div class="info-item">
          <label>任务名称：</label>
          <span>{{ credentialsData.taskName || '-' }}</span>
        </div>
        <div class="info-item">
          <label>完成时间：</label>
          <span>{{ formatDateTime(credentialsData.taskFinishTime) }}</span>
        </div>
      </div>

      <div class="credentials-section">
        <h4>完成凭证文件</h4>
        <div v-if="credentialsData.taskCredentials && credentialsData.taskCredentials.length > 0" class="credentials-list">
          <div
            v-for="(credential, index) in credentialsData.taskCredentials"
            :key="index"
            class="credential-item"
          >
            <div class="credential-info">
              <div class="credential-name">
                <i :class="getFileIcon(credential.fileName)"></i>
                <span>{{ credential.fileName || `文件${index + 1}` }}</span>
              </div>
              <div class="credential-meta">
                <span class="file-size">{{ formatFileSize(credential.fileSize) }}</span>
                <span class="upload-time">{{ formatDateTime(credential.uploadTime) }}</span>
              </div>
            </div>
            <div class="credential-actions">
              <el-button
                type="text"
                size="small"
                @click="handlePreview(credential)"
                v-if="isPreviewable(credential.fileName)"
              >
                预览
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleDownload(credential)"
              >
                下载
              </el-button>
            </div>
          </div>
        </div>
        <div v-else class="no-credentials">
          <i class="el-icon-document"></i>
          <p>暂无完成凭证</p>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewDialogVisible"
      width="60%"
      :close-on-click-modal="true"
      append-to-body
    >
      <div class="image-preview">
        <img :src="previewImageUrl" alt="预览图片" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { getToken } from '../../../../helpers/token'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    credentialsData: {
      type: Object,
      default: () => ({
        taskName: '',
        taskFinishTime: '',
        taskCredentials: []
      })
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      previewDialogVisible: false,
      previewImageUrl: '',
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.previewDialogVisible = false
      this.previewImageUrl = ''
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },

    formatFileSize(size) {
      if (!size) return '-'
      if (size < 1024) return size + ' B'
      if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
      return (size / (1024 * 1024)).toFixed(1) + ' MB'
    },

    getFileIcon(fileName) {
      if (!fileName) return 'el-icon-document'
      
      const ext = fileName.toLowerCase().split('.').pop()
      const iconMap = {
        // 图片
        'jpg': 'el-icon-picture',
        'jpeg': 'el-icon-picture',
        'png': 'el-icon-picture',
        'gif': 'el-icon-picture',
        'bmp': 'el-icon-picture',
        'webp': 'el-icon-picture',
        // 文档
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-document',
        'xlsx': 'el-icon-document',
        'ppt': 'el-icon-document',
        'pptx': 'el-icon-document',
        'txt': 'el-icon-document',
        // 压缩包
        'zip': 'el-icon-folder',
        'rar': 'el-icon-folder',
        '7z': 'el-icon-folder',
        // 视频
        'mp4': 'el-icon-video-camera',
        'avi': 'el-icon-video-camera',
        'mov': 'el-icon-video-camera',
        'wmv': 'el-icon-video-camera'
      }
      
      return iconMap[ext] || 'el-icon-document'
    },

    isPreviewable(fileName) {
      if (!fileName) return false
      
      const ext = fileName.toLowerCase().split('.').pop()
      const previewableExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      
      return previewableExts.includes(ext)
    },

    async handlePreview(credential) {
      if (!this.isPreviewable(credential.fileName)) {
        this.$message.warning('该文件类型不支持预览')
        return
      }

      try {
        // 构建预览URL
        this.previewImageUrl = `${window.env?.apiPath}/api/supplier/task/downloadCredential/${credential.fileId}?token=${getToken()}`
        this.previewDialogVisible = true
      } catch (error) {
        console.error('预览失败：', error)
        this.$message.error('预览失败，请重试')
      }
    },

    async handleDownload(credential) {
      try {
        // 创建下载链接
        const downloadUrl = `${window.env?.apiPath}/api/supplier/task/downloadCredential/${credential.fileId}?token=${getToken()}`
        
        // 创建隐藏的下载链接
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = credential.fileName || `文件_${credential.fileId}`
        link.style.display = 'none'
        
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        this.$message.success('下载开始')
      } catch (error) {
        console.error('下载失败：', error)
        this.$message.error('下载失败，请重试')
      }
    }
  }
}
</script>

<style scoped>
.credentials-content {
  padding: 20px 0;
}

.task-info {
  margin-bottom: 30px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.credentials-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.credentials-list {
  max-height: 400px;
  overflow-y: auto;
}

.credential-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.credential-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.credential-info {
  flex: 1;
}

.credential-name {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.credential-name i {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.credential-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.credential-actions {
  display: flex;
  gap: 8px;
}

.credential-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.no-credentials {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.no-credentials i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.no-credentials p {
  font-size: 14px;
  margin: 0;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.image-preview {
  text-align: center;
  max-height: 70vh;
  overflow: auto;
}

.image-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
.credentials-list::-webkit-scrollbar {
  width: 6px;
}

.credentials-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.credentials-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.credentials-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
