<template>
  <div class="merchant-users-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
            <el-form-item>
              <el-input
                v-model="conditions.filters.nameOrCellphone"
                placeholder="请输入用户姓名或手机号"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="conditions.filters.roleId"
                placeholder="请选择角色"
                clearable
              >
                <el-option
                  v-for="role in roleOptions"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 表头操作区 -->
      <div class="table-header">
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <i class="el-icon-plus"></i>
            新建用户
          </el-button>
        </div>
      </div>
      <el-table
        :data="data"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column prop="name" label="姓名" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="cellphone" label="手机号" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.cellphone || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="roles" label="关联角色" min-width="200" :formatter="formatRoles" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" min-width="160" :formatter="formatDateTime" />
        <el-table-column prop="modifyTime" label="更新时间" min-width="160" :formatter="formatDateTime" />
        <el-table-column label="状态" min-width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.disabled ? 'danger' : 'success'" 
              size="medium"
              effect="light"
            >
              {{ scope.row.disabled ? '已禁用' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="240" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                type="text"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="!scope.row.disabled"
                type="text"
                size="small"
                @click="handleToggleStatus(scope.row, true)"
              >
                禁用
              </el-button>
              <el-button
                v-if="scope.row.disabled"
                type="text"
                size="small"
                @click="handleToggleStatus(scope.row, false)"
              >
                启用
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          nameOrCellphone: null,
          roleId: null,
          disabled: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      tableHeight: 500,
      roleOptions: []
    }
  },
  async created() {
    
    await this.loadRoles()
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadRoles() {
      var roles = {
        offset: 0,
        limit: 1000,
        withTotal: true,
        filters: {
          name: '',
          disabled: null
        }
      }
      const [err, r] = await client.customerGetRoles({ body: roles })
      if (err) {
        handleError(err)
        return
      }
      this.roleOptions = r.data.list || []
    },

    async getList() {
      this.loading = true
      if (this.$route.query.roleId) {
        this.conditions.filters.roleId = this.$route.query.roleId * 1
      }

      const payload = {
        ...this.conditions
      }

      const [err, r] = await client.customerGetMembers({
        body: payload
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    
    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          nameOrCellphone: null,
          roleId: null,
          disabled: null
        }
      }
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return '-'
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    formatRoles(row, column, cellValue) {
      if (!cellValue || cellValue.length === 0) return '-'
      return cellValue.map(role => role.name).join(', ')
    },

    handleAdd() {
      this.$router.push('/members/new')
    },

    handleEdit(row) {
      const roleIds = row.roles.map(role => role.id).join(',')
      this.$router.push(
        `/members/${row.memberId}/edit?name=${row.name}&cellphone=${row.cellphone}&roleIds=${roleIds}`
      )
    },

    async handleToggleStatus(row, disabled) {
      const action = disabled ? '禁用' : '启用'
      try {
        await this.$confirm(`确定要${action}用户 "${row.name}" 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const [err] = await client.customerDisableMember({
          body: { id: row.memberId, disabled }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess(`${action}成功`)
        this.getList()
      } catch (error) {
        // User cancelled, do nothing
      }
    },

    async handleDelete(row) {
      try {
        await this.$confirm(
          `确定要删除用户 "${row.name}" 吗? 此操作无法撤销。`,
          '警告',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'error'
          }
        )

        const [err] = await client.customerRemoveMember({
          body: { id: row.memberId, disabled: true }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('删除成功')
        if (this.conditions.offset > 0 && this.data.length === 1) {
          this.conditions.offset -= this.conditions.limit
          this.getList()
        } else {
          this.getList()
        }
      } catch (error) {
        // User cancelled, do nothing
      }
    },

    setTableHeight() {
      const windowHeight = window.innerHeight;
      const searchContainer = this.$el.querySelector('.search-container');
      const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
      const tableHeader = this.$el.querySelector('.table-header');
      const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
      const pagination = this.$el.querySelector('.pagination-container');
      const paginationHeight = pagination ? pagination.offsetHeight : 40;
      const padding = 40;

      const availableHeight = windowHeight - searchHeight - tableHeaderHeight - paginationHeight - padding;

      if (this.data.length <= 5) {
        this.tableHeight = null;
      } else {
        const minHeight = 300;
        const maxHeight = windowHeight - searchHeight - tableHeaderHeight - paginationHeight - padding - 5;
        this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
      }

      this.tableHeight = Math.floor(this.tableHeight);
    }
  }
}
</script>

<style scoped>
.merchant-users-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 200px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.search-main .el-form-item .el-form-item__content {
  width: 200px;
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.search-actions .el-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}



.el-form-item .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.table-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 12px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions .el-button {
  border-radius: 4px;
  padding: 9px 20px;
  transition: all 0.3s;
}

.table-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.table-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 - 查询按钮始终固定在右上角 */
@media screen and (max-width: 1200px) {
  .search-header {
    padding-right: 180px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 180px;
  }
}

@media screen and (max-width: 992px) {
  .search-header {
    padding-right: 160px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 160px;
  }
}

/* 小屏幕：查询按钮仍然固定在右上角，但缩小间距 */
@media screen and (max-width: 768px) {
  .search-header {
    padding-right: 140px;
  }

  .search-main {
    flex-direction: column;
    gap: 12px;
  }

  .search-main .el-form-item {
    width: 100%;
  }

  .search-main .el-form-item .el-form-item__label {
    width: 80px;
    text-align: right;
    margin-right: 12px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 200px;
  }

  .more-form-items {
    flex-direction: column;
    gap: 12px;
  }

  .more-form-items .el-form-item {
    width: 100%;
  }

  .more-form-items .el-form-item .el-form-item__label {
    width: 80px;
    text-align: right;
    margin-right: 12px;
  }

  .more-form-items .el-form-item .el-form-item__content {
    width: 200px;
  }
}

/* 超小屏幕：查询按钮移到下方，避免空间不足 */
@media screen and (max-width: 480px) {
  .search-header {
    padding-right: 0;
    min-height: auto;
  }

  .search-actions {
    position: static;
    margin-top: 16px;
    justify-content: flex-start;
    height: auto;
  }

  .search-main .el-form-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-main .el-form-item .el-form-item__label {
    width: auto;
    text-align: left;
    margin-right: 0;
    margin-bottom: 4px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 100%;
  }

  .more-form-items .el-form-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .more-form-items .el-form-item .el-form-item__label {
    width: auto;
    text-align: left;
    margin-right: 0;
    margin-bottom: 4px;
  }

  .more-form-items .el-form-item .el-form-item__content {
    width: 100%;
  }
}
</style>
