<template>
  <el-dialog
    title="任务完成凭证"
    :visible.sync="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    class="task-credentials-dialog"
    @close="handleClose"
  >
    <div v-loading="loading" class="credentials-content">
      <!-- 任务信息 -->
      <el-form label-width="100px" class="credentials-form">
        <el-form-item label="任务名称：">
          <span class="task-name-display">{{ credentialsData.taskName || '-' }}</span>
        </el-form-item>

        <el-form-item label="完成时间：">
          <span class="task-finish-time">{{ formatDateTime(credentialsData.taskFinishTime) || '-' }}</span>
        </el-form-item>

        <el-form-item label="完成凭证：">
          <div class="credentials-container">
            <!-- 文件预览网格 -->
            <div class="file-grid">
              <!-- 已上传的文件 -->
              <div
                v-for="(fileId, index) in credentialsData.taskCredentials"
                :key="index"
                class="file-preview-item"
              >
                <!-- 图片预览 -->
                <div v-if="!fileLoadErrors[fileId]" class="image-preview">
                  <img
                    :src="getFilePreviewUrl(fileId)"
                    :alt="`凭证${index + 1}`"
                    class="preview-image"
                    @error="handleImageError(fileId)"
                    @load="handleImageLoad(fileId)"
                  />
                  <div class="file-overlay">
                    <span class="file-name">凭证{{ index + 1 }}</span>
                    <div class="file-actions">
                      <i class="el-icon-zoom-in" @click="previewImage(fileId)" title="预览"></i>
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>

                <!-- 非图片文件预览 -->
                <div v-else class="file-preview">
                  <div class="file-icon-container">
                    <i
                      :class="getFileIcon(fileId).icon"
                      class="file-type-icon"
                      :style="{ color: getFileIcon(fileId).color }"
                    ></i>
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="`凭证${index + 1}`">凭证{{ index + 1 }}</span>
                  </div>
                  <div class="file-overlay">
                    <span class="file-name">凭证{{ index + 1 }}</span>
                    <div class="file-actions">
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无凭证提示 -->
              <div v-if="!credentialsData.taskCredentials || credentialsData.taskCredentials.length === 0" class="no-credentials">
                <i class="el-icon-document"></i>
                <span>暂无完成凭证</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from '../../../../helpers/token'

export default {
  name: 'TaskCredentialsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    credentialsData: {
      type: Object,
      default: () => ({
        taskName: '',
        taskFinishTime: '',
        taskCredentials: []
      })
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      fileLoadErrors: {} // 记录文件加载错误的对象
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      // 重置文件加载错误状态
      this.fileLoadErrors = {}
    },

    handleImageError(fileId) {
      // 图片加载失败，标记为非图片文件
      this.$set(this.fileLoadErrors, fileId, true)
    },

    handleImageLoad(fileId) {
      // 图片加载成功，确保不在错误列表中
      this.$set(this.fileLoadErrors, fileId, false)
    },

    formatDateTime(value) {
      if (!value) return '-'
      const date = new Date(value)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 文件处理相关方法
    isImageFile(fileId) {
      // 由于只有fileId，我们先尝试作为图片处理
      // 如果加载失败，会在img的onerror事件中处理
      return true
    },

    getFileIcon(fileId) {
      // 由于只有fileId，无法确定文件类型，使用通用文档图标
      return { icon: 'el-icon-document', color: '#409eff' }
    },

    getFilePreviewUrl(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    },

    previewImage(fileId) {
      const imageUrl = this.getFilePreviewUrl(fileId)
      this.$alert(`<img src="${imageUrl}" style="max-width: 600px; max-height: 500px;" />`, '凭证预览', {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false,
        showClose: true,
        customClass: 'image-preview-dialog'
      })
    },

    downloadFile(fileId) {
      window.location.href = window.env?.apiPath + `/api/public/downloadFile/${fileId}`
    }
  }
}
</script>

<style scoped>
.task-credentials-dialog {
  .credentials-form {
    .task-name-display,
    .task-finish-time {
      font-weight: 500;
      color: #303133;
    }
  }

  .credentials-container {
    .file-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 12px;
      min-height: 120px;
    }

    .file-preview-item {
      position: relative;
      width: 120px;
      height: 120px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      overflow: hidden;
      background: #fff;

      &:hover .file-overlay {
        opacity: 1;
      }
    }

    .image-preview {
      position: relative;
      width: 100%;
      height: 100%;

      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file-preview {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .file-icon-container {
        .file-type-icon {
          font-size: 20px;
        }
      }

      .file-info {
        text-align: center;
        width: 100%;

        .file-name {
          display: block;
          font-size: 12px;
          color: #606266;
          margin-bottom: 4px;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }

    .file-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 8px;
      opacity: 0;
      transition: opacity 0.3s;

      .file-name {
        font-size: 12px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .file-actions {
        display: flex;
        justify-content: center;
        gap: 12px;

        i {
          cursor: pointer;
          font-size: 16px;
          padding: 4px;
          border-radius: 2px;
          transition: background-color 0.3s;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }

    .no-credentials {
      grid-column: 1 / -1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 16px;
      }

      span {
        font-size: 14px;
      }
    }
  }

  .dialog-footer {
    text-align: center;
  }
}

/* 图片预览对话框样式 */
:global(.image-preview-dialog) {
  .el-message-box__content {
    text-align: center;
  }

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}
</style>
