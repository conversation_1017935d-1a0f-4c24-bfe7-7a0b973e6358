<template>
  <div class="invoices-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
            <el-form-item>
              <el-input
                v-model="conditions.filters.id"
                placeholder="请输入发票申请ID"
                style="width: 300px"
                clearable
                @input="handleIdInput"
              />
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="createTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="申请时间"
                end-placeholder="申请时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 350px"
                @change="handleCreateTimeChange"
              />
            </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="data"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        row-key="id"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column
          prop="id"
          label="发票申请ID"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.id || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="billMonth"
          label="账单月"
          width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.billMonth || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="发票抬头"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.title || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="typeDesc"
          label="发票类型"
          width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.typeDesc || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="supplierCorporationName"
          label="开票主体"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.supplierCorporationName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="fee"
          label="开票金额"
          width="150"
        >
          <template slot-scope="scope">
            {{ scope.row.fee || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="申请时间"
          width="160"
          :formatter="formatDateTime"
        ></el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.statusDesc)"
              size="small"
            >
              {{ scope.row.statusDesc || '-' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" min-width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button
              v-if="scope.row.statusDesc === '已开票'"
              type="text"
              size="small"
              @click="handleDownload(scope.row)"
            >
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'Invoices',
  data() {
    return {
      createTimeRange: [],
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          id: '',
          createTimeStart: '',
          createTimeEnd: ''
        }
      },
      total: 0,
      data: [],
      loading: true,
      tableHeight: 500
    }
  },
  async created() {
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    // 处理发票申请ID输入，只允许数字
    handleIdInput(value) {
      // 只保留数字字符
      const numericValue = value.replace(/[^\d]/g, '')
      this.conditions.filters.id = numericValue
    },
    async getList() {
      this.loading = true
      try {
        const [err, r] = await client.costomerInvoicesList({
          body: this.conditions
        })
        if (err) {
          handleError(err)
          return
        }
        this.data = r.data.list || []
        this.total = r.data.total || 0
      } finally {
        this.loading = false
      }
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.createTimeRange = []
      this.conditions.filters = {
        id: '',
        createTimeStart: '',
        createTimeEnd: ''
      }
      this.onSearch()
    },

    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0] + ' 00:00:00'
        this.conditions.filters.createTimeEnd = value[1] + ' 23:59:59'
      } else {
        this.conditions.filters.createTimeStart = null
        this.conditions.filters.createTimeEnd = null
      }
    },

    handleView(row) {
      this.$router.push(`/invoices/${row.id}`)
    },

    handleDownload(row) {
      window.location.href =
        window.env?.apiPath + `/api/public/downloadFile/${row.invoiceFile}`
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return '-'
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    getStatusTagType(status) {
      const typeMap = {
        '待开票': 'warning',
        '已开票': 'success',
        '已退回': 'danger'
      }
      return typeMap[status] || 'info'
    },

    setTableHeight() {
      this.$nextTick(() => {
        const windowHeight = window.innerHeight;
        const searchContainer = this.$el.querySelector('.search-container');
        const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
        const pagination = this.$el.querySelector('.pagination-container');
        const paginationHeight = pagination ? pagination.offsetHeight : 40;
        const padding = 80;

        const availableHeight = windowHeight - searchHeight - paginationHeight - padding;

        if (this.data.length <= 5) {
          this.tableHeight = 500;
        } else {
          const minHeight = 300;
          const maxHeight = windowHeight - searchHeight - paginationHeight - padding - 5;
          this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
        }

        this.tableHeight = Math.floor(this.tableHeight);
      })
    }
  }
}
</script>

<style scoped>
.invoices-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
  width: 100px;
  text-align: right;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-actions .el-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.el-form-item .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .search-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-main {
    margin-bottom: 16px;
    width: 100%;
  }

  .search-main .el-form-item {
    width: 100%;
    margin-bottom: 12px;
  }

  .search-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .search-actions .el-button {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
</style>
