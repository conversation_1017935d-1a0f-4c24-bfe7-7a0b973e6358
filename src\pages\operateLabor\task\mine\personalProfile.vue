<template>
  <div>
    <div>
      服务合同：
      <span>切换</span>
    </div>
    <div v-for="(item, index) in profileList" :key="index">
      {{ item }}
      <Icon name="arrow" />
    </div>
  </div>
</template>
<script>
import { Icon } from 'vant'

export default {
  components: {
    Icon
  },
  data() {
    return {
      profileList: [
        '基本信息：',
        '项目经历：',
        '附件信息：',
        '银行卡信息：'
      ]
    }
  },
  methods: {

  }
}
</script>